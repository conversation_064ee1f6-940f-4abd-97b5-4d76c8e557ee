#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_parser.h>
#include <yaProtoRecord/precord.h>
#include <gmock/gmock.h>

using namespace testing;

#define CHAR_ARRAY_LEN(array) (sizeof(array) - 1)

const uint8_t kInputOptionMsg[] = "OPTIONS rtsp://************:554/Streaming/Channels/701?transportmode=unicast RTSP/1.0\r\n"
"CSeq: 1\r\n"
"User-Agent: Lavf59.23.100\r\n"
"\r\n";

const char *precord_get_str_fvalue(precord_t *precord, const char *fname)
{
    ya_fvalue_t *fvalue = precord_fvalue_get(precord, fname);
    if (NULL == fvalue)
    {
        return NULL;
    }

    return ya_fvalue_get_string(fvalue);
}

class rtsp_parser : public testing::Test
{
public:
    void SetUp() override
    {
        engine_ = nxt_engine_create(NULL);
        pschema_db_t* db = nxt_engine_get_schemadb(engine_);
        precord_ = precord_create_ex(db, PRECORD_FLAG_NONE);
        precord_layer_put_new_layer(precord_, "rtsp");
    }

    void TearDown() override
    {
        precord_destroy(precord_);
        nxt_engine_destroy(engine_);
    }

protected:
    nxt_engine_t *engine_  = NULL;
    precord_t    *precord_ = NULL;
};

TEST_F(rtsp_parser, single_msg_in_whole_buf)
{
    ya_allocator_t *alloc = ya_allocator_get_default();
    nxt_parser_t *parser = nxt_parser_create_wa(alloc, nxt_parser_rtsp_init);
    int consumeLen = nxt_parser_rtsp_parse(parser, kInputOptionMsg, CHAR_ARRAY_LEN(kInputOptionMsg), precord_);
    ASSERT_EQ(CHAR_ARRAY_LEN(kInputOptionMsg), consumeLen);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE, nxt_parser_get_status(parser));

    EXPECT_STREQ("OPTIONS",       precord_get_str_fvalue(precord_, "method"));
    EXPECT_STREQ("Lavf59.23.100", precord_get_str_fvalue(precord_, "user_agent"));

    nxt_parser_destroy_wa(alloc, parser);
}

TEST_F(rtsp_parser, DISABLED_msg_split_in_multiple_noncontinial_buf)
{
    ya_allocator_t *alloc = ya_allocator_get_default();
    nxt_parser_t *parser = nxt_parser_create_wa(alloc, nxt_parser_rtsp_init);

    const uint8_t kMsgPart1[] = "OPTIONS rtsp://************:554/Streaming/Channels/701?transportmode=unicast RTSP/1.0\r\n"
        "CSeq: 1\r\n"
        "User-Agent: Lavf";
    const uint8_t kMsgPart2[] = "59.23.100\r\n" "\r\n";

    // parse part1, user_agent 字段无法正常提取;
    int consumeLen = nxt_parser_rtsp_parse(parser, kMsgPart1, CHAR_ARRAY_LEN(kMsgPart1), precord_);
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart1), consumeLen);
    ASSERT_EQ(NXT_PSTATUS_PARTIAL, nxt_parser_get_status(parser));
    EXPECT_STREQ("OPTIONS", precord_get_str_fvalue(precord_, "method"));
    EXPECT_STREQ(NULL,      precord_get_str_fvalue(precord_, "user_agent"));

    // parse part2, useragent 提取出来后被截断;
    consumeLen = nxt_parser_rtsp_parse(parser, kMsgPart2, CHAR_ARRAY_LEN(kMsgPart2), precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE, nxt_parser_get_status(parser));
    EXPECT_STREQ("Lavf",      precord_get_str_fvalue(precord_, "user_agent"));

    nxt_parser_destroy_wa(alloc, parser);
}

TEST_F(rtsp_parser, stream_parse_ringbuf_hold_whole_msg)
{
    const uint8_t kMsgPart1[] = "OPTIONS rtsp://************:554/Streaming/Channels/701?transportmode=unicast RTSP/1.0\r\n"
        "CSeq: 1\r\n"
        "User-Agent: Lavf";
    const uint8_t kMsgPart2[] = "59.23.100\r\n\r\n";

    ya_allocator_t *alloc  = ya_allocator_get_default();
    nxt_parser_t   *parser = nxt_parser_create_wa(alloc, nxt_parser_rtsp_init);

    // 准备 ringbuf
    nxt_ringbuf_t* rbuf = nxt_ringbuf_create_wa(alloc, 200);

    int appendLen = nxt_ringbuf_push_back(rbuf, kMsgPart1, CHAR_ARRAY_LEN(kMsgPart1));
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart1), appendLen);
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart1), nxt_ringbuf_get_data_length(rbuf));

    // parse part1, user_agent 字段无法正常提取;
    int consumeLen = nxt_parser_rtsp_parse(parser, nxt_ringbuf_get_data(rbuf), nxt_ringbuf_get_data_length(rbuf), precord_);
    ASSERT_NE(CHAR_ARRAY_LEN(kMsgPart1), consumeLen);
    ASSERT_EQ(NXT_PSTATUS_PARTIAL, nxt_parser_get_status(parser));
    EXPECT_STREQ("OPTIONS", precord_get_str_fvalue(precord_, "method"));
    EXPECT_STREQ(NULL,      precord_get_str_fvalue(precord_, "user_agent"));

    nxt_ringbuf_pop_front(rbuf, consumeLen);

    // WARNING: ringbuf 尾部的空间不足，将发生 memmove
    appendLen = nxt_ringbuf_push_back(rbuf, kMsgPart2, CHAR_ARRAY_LEN(kMsgPart2));
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart2), appendLen);

    // parse part2
    consumeLen = nxt_parser_rtsp_parse(parser, nxt_ringbuf_get_data(rbuf), nxt_ringbuf_get_data_length(rbuf), precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE, nxt_parser_get_status(parser));
    EXPECT_STREQ("Lavf59.23.100",      precord_get_str_fvalue(precord_, "user_agent"));

    nxt_ringbuf_destroy_wa(alloc, rbuf);
    nxt_parser_destroy_wa(alloc, parser);
}

TEST_F(rtsp_parser, stream_parse_ringbuf_no_backspace_hold_whole)
{
    // 一个完整消息被拆分为两部分送给 parse, 并且 "User-Agent" 字段的 value 被截断;
    const uint8_t kMsgPart1[] = "OPTIONS rtsp://************:554/Streaming/Channels/701?transportmode=unicast RTSP/1.0\r\n"
        "CSeq: 1\r\n"
        "User-Agent: Lavf";
    const uint8_t kMsgPart2[] = "59.23.100\r\n" "\r\n";

    ya_allocator_t *alloc  = ya_allocator_get_default();
    nxt_parser_t   *parser = nxt_parser_create_wa(alloc, nxt_parser_rtsp_init);
    nxt_ringbuf_t  *rbuf   = nxt_ringbuf_create_wa(alloc, 120); // 120 不足以容纳下完整的消息

    int appendLen = nxt_ringbuf_push_back(rbuf, kMsgPart1, CHAR_ARRAY_LEN(kMsgPart1));
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart1), appendLen);
    ASSERT_EQ(CHAR_ARRAY_LEN(kMsgPart1), nxt_ringbuf_get_data_length(rbuf));

    // parse part1, parse 发现需要提取的字段被截断，吐出它不消耗;
    int consumeLen = nxt_parser_rtsp_parse(parser, nxt_ringbuf_get_data(rbuf), nxt_ringbuf_get_data_length(rbuf), precord_);
    ASSERT_EQ(NXT_PSTATUS_PARTIAL, nxt_parser_get_status(parser));

    // consume_len 应该只消耗到了 "User-Agent: [L]avf" 的位置;
    ASSERT_STREQ("Lavf", (const char *)&kMsgPart1[consumeLen]);

    ASSERT_STREQ("OPTIONS", precord_get_str_fvalue(precord_, "method"));
    ASSERT_STREQ(NULL,      precord_get_str_fvalue(precord_, "user_agent"));

    nxt_ringbuf_pop_front(rbuf, consumeLen);

    // WARNING: ringbuf 尾部的空间不足，将发生 memmove
    appendLen = nxt_ringbuf_push_back(rbuf, kMsgPart2, CHAR_ARRAY_LEN(kMsgPart2));

    // rbuf 中的内容此时为 "Lavf";
    std::string ringBuf{(const char *)nxt_ringbuf_get_data(rbuf), nxt_ringbuf_get_data_length(rbuf)};
    ASSERT_THAT(ringBuf, StartsWith("Lavf59.23.100\r\n\r\n"));

    // parse part2
    // 此时 ringbuf: “Lavf59.23.100\r\n\r\n2.20. ......”
    consumeLen = nxt_parser_rtsp_parse(parser, nxt_ringbuf_get_data(rbuf), nxt_ringbuf_get_data_length(rbuf), precord_);
    ASSERT_EQ(NXT_PSTATUS_COMPLETE, nxt_parser_get_status(parser));
    ASSERT_STREQ("Lavf59.23.100", precord_get_str_fvalue(precord_, "user_agent"));

    nxt_ringbuf_destroy_wa(alloc, rbuf);
    nxt_parser_destroy_wa(alloc, parser);
}

TEST_F(rtsp_parser, stream_parse_ringbuf_no_totalspace_hold_whole)
{
    // 一个完整消息被拆分为两部分送给 parse, 并且 "User-Agent" 字段的 value 被截断;
    const uint8_t kMsgPart1[] = "OPTIONS rtsp://************:554/Streaming/Channels/701?transportmode=unicast RTSP/1.0\r\n"
        "CSeq: 1\r\n"
        "User-Agent: Lavf";

    ya_allocator_t *alloc = ya_allocator_get_default();
    nxt_ringbuf_t *rbuf   = nxt_ringbuf_create_wa(alloc, 50); // 50 不足以容纳下 msg_part1, 将抛出异常;
    EXPECT_THROW(nxt_ringbuf_push_back(rbuf, kMsgPart1, CHAR_ARRAY_LEN(kMsgPart1)), std::exception);

    nxt_ringbuf_destroy_wa(alloc, rbuf);
}
