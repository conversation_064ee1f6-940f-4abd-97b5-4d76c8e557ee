#include <recognizer.h>
#include <dissector.h>
#include <yaEngineNext/nxt_engine.h>
#include <gmock/gmock.h>

using namespace testing;

class Recongnizer_test : public testing::Test
{
public:
    void SetUp() override
    {
        engine_ = nxt_engine_create(NULL);

    }

    void TearDown() override
    {
        nxt_engine_destroy(engine_);
    }

    nxt_dissector_t* recogInText(nxt_ProtoRecognizer &recog, uint16_t port, const std::string &text)
    {
        return recog.recognizeProto(port, (uint8_t *)text.c_str(), text.size());
    }

protected:
    nxt_engine_t *engine_  = NULL;
};

#define NXT_ANY_PORT    0
#define NXT_ANY_PAYLOAD NULL

TEST_F(Recongnizer_test, basic)
{
    nxt_Dissector* dissectorHttp = nxt_dissector_get_by_name("http");
    nxt_Dissector* dissectorRtsp = nxt_dissector_get_by_name("rtsp");

    nxt_ProtoRecognizer recog;
    recog.registerPattern(80,           "^GET",          dissectorHttp); // A: port + payload
    recog.registerPattern(NXT_ANY_PORT, "^POST",         dissectorHttp); // B: payload-only
    recog.registerPattern(80,           "^HEAD",         dissectorHttp); // C: port + payload
    recog.registerPattern(NXT_ANY_PORT, "^DELETE",       dissectorHttp); // D: payload-only
    recog.registerPattern(554,          NXT_ANY_PAYLOAD, dissectorRtsp); // E: port-only

    recog.registerDone();

    ASSERT_EQ(dissectorHttp, recogInText(recog, 80,   "GET /cgi_bin/foo"));             // 匹配成功 A
    ASSERT_EQ(NULL,          recogInText(recog, 8000, "GET /cgi_bin/foo"));             // 匹配失败
    ASSERT_EQ(dissectorHttp, recogInText(recog, 8000, "POST /cgi_bin/foo"));            // 匹配成功 B
    ASSERT_EQ(dissectorHttp, recogInText(recog, 80,   "POST /cgi_bin/foo"));            // 匹配成功 B
    ASSERT_EQ(dissectorHttp, recogInText(recog, 80,   "HEAD /cgi_bin/foo"));            // 匹配成功 C
    ASSERT_EQ(dissectorHttp, recogInText(recog, 9000, "DELETE /path/hello"));           // 匹配成功 D
    ASSERT_EQ(NULL,          recogInText(recog, 80,   "OPTION /cgi_bin/foo"));          // 匹配失败
    ASSERT_EQ(NULL,          recogInText(recog, 80,   "OPTION /cgi_bin/POST"));         // 匹配失败
    ASSERT_EQ(NULL,          recogInText(recog, 80,   "OPTION /cgi_bin/HEAD"));         // 匹配失败
    ASSERT_EQ(dissectorRtsp, recogInText(recog, 554,  "DESCRIBE rtsp://172.20.9.123")); // 匹配成功 E
    ASSERT_EQ(dissectorRtsp, recogInText(recog, 554,  "xxxxxxxx rtsp://172.20.9.123")); // 匹配成功 E
}
