#include "sip_rtp_correlator.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cctype>

namespace media {

SipRtpCorrelator::SipRtpCorrelator(rtp::RtpEngine& rtp_engine, SessionManager& session_manager)
    : rtp_engine_(rtp_engine), session_manager_(session_manager) {
}

SipRtpCorrelator::~SipRtpCorrelator() {
}

bool SipRtpCorrelator::processSipMessage(const std::string& sip_message, 
                                        const std::string& src_ip, 
                                        const std::string& dst_ip) {
    SipCallInfo call_info;
    
    if (!parseSipMessage(sip_message, call_info)) {
        return false;
    }
    
    call_info.caller_ip = src_ip;
    call_info.callee_ip = dst_ip;
    
    // Store or update call information
    auto it = call_info_.find(call_info.call_id);
    if (it == call_info_.end()) {
        // New call
        call_info_[call_info.call_id] = call_info;
        
        // Create media session
        uint32_t session_id = session_manager_.createSession(call_info.call_id, "SIP");
        MediaSession* session = session_manager_.getSession(session_id);
        
        if (session && !call_info.media_descriptions.empty()) {
            // Add SDP information
            std::string sdp = SipSdpParser::extractSdp(sip_message);
            if (!sdp.empty()) {
                session->addSdpInfo(sdp);
            }
        }
        
        std::cout << "New SIP call: " << call_info.call_id 
                 << " (" << call_info.method << ")" << std::endl;
    } else {
        // Update existing call
        it->second = call_info;
        std::cout << "Updated SIP call: " << call_info.call_id 
                 << " (" << call_info.method << ")" << std::endl;
    }
    
    return true;
}

bool SipRtpCorrelator::processRtpPacket(const rtp::RtpPacket& packet,
                                       const std::string& src_ip,
                                       const std::string& dst_ip,
                                       uint16_t src_port,
                                       uint16_t dst_port) {
    // Process packet through RTP engine
    rtp_engine_.processRtpPacket(packet);
    
    // Try to correlate with existing SIP call
    if (correlateRtpWithSip(packet, src_ip, dst_ip, src_port, dst_port)) {
        return true;
    }
    
    // If no correlation found, create a basic one
    RtpCorrelation correlation;
    correlation.ssrc = packet.ssrc;
    correlation.local_ip = src_ip;
    correlation.remote_ip = dst_ip;
    correlation.local_port = src_port;
    correlation.remote_port = dst_port;
    correlation.payload_type = packet.payload_type;
    correlation.codec_name = getCodecNameFromPayloadType(packet.payload_type);
    correlation.media_type = getMediaTypeFromPayloadType(packet.payload_type);
    correlation.call_id = "unknown_" + std::to_string(packet.ssrc);
    
    correlations_[packet.ssrc] = correlation;
    
    return true;
}

bool SipRtpCorrelator::correlateRtpWithSip(const rtp::RtpPacket& packet,
                                          const std::string& src_ip,
                                          const std::string& dst_ip,
                                          uint16_t src_port,
                                          uint16_t dst_port) {
    // Look for matching SIP call based on IP addresses and ports
    for (auto& call_pair : call_info_) {
        const SipCallInfo& call_info = call_pair.second;
        
        // Check if IPs match
        bool ip_match = (src_ip == call_info.caller_ip && dst_ip == call_info.callee_ip) ||
                       (src_ip == call_info.callee_ip && dst_ip == call_info.caller_ip);
        
        if (!ip_match) {
            continue;
        }
        
        // Check if port matches any media description
        for (const auto& media : call_info.media_descriptions) {
            if (src_port == media.port || dst_port == media.port) {
                // Found matching media stream
                RtpCorrelation correlation;
                correlation.ssrc = packet.ssrc;
                correlation.call_id = call_info.call_id;
                correlation.local_ip = src_ip;
                correlation.remote_ip = dst_ip;
                correlation.local_port = src_port;
                correlation.remote_port = dst_port;
                correlation.payload_type = packet.payload_type;
                
                // Try to find codec name from SDP
                auto codec_it = media.codecs.find(packet.payload_type);
                if (codec_it != media.codecs.end()) {
                    correlation.codec_name = codec_it->second;
                } else {
                    correlation.codec_name = getCodecNameFromPayloadType(packet.payload_type);
                }
                
                if (media.media_type == "audio") {
                    correlation.media_type = MediaType::AUDIO;
                } else if (media.media_type == "video") {
                    correlation.media_type = MediaType::VIDEO;
                } else {
                    correlation.media_type = getMediaTypeFromPayloadType(packet.payload_type);
                }
                
                correlations_[packet.ssrc] = correlation;
                call_streams_[call_info.call_id].push_back(packet.ssrc);
                
                std::cout << "Correlated RTP stream SSRC=0x" << std::hex << packet.ssrc 
                         << std::dec << " with call " << call_info.call_id << std::endl;
                
                return true;
            }
        }
    }
    
    return false;
}

bool SipRtpCorrelator::parseSipMessage(const std::string& message, SipCallInfo& call_info) {
    call_info.call_id = extractCallId(message);
    call_info.method = extractMethod(message);
    call_info.from_uri = extractFromUri(message);
    call_info.to_uri = extractToUri(message);
    call_info.cseq = extractCSeq(message);
    
    if (call_info.call_id.empty()) {
        return false;
    }
    
    // Parse SDP if present
    std::string sdp = SipSdpParser::extractSdp(message);
    if (!sdp.empty()) {
        parseSdp(sdp, call_info.media_descriptions);
    }
    
    return true;
}

std::string SipRtpCorrelator::extractCallId(const std::string& message) {
    return SipSdpParser::extractHeader(message, "Call-ID");
}

std::string SipRtpCorrelator::extractMethod(const std::string& message) {
    std::istringstream iss(message);
    std::string first_line;
    if (std::getline(iss, first_line)) {
        std::istringstream line_iss(first_line);
        std::string method;
        line_iss >> method;
        return method;
    }
    return "";
}

std::string SipRtpCorrelator::extractFromUri(const std::string& message) {
    return SipSdpParser::extractHeader(message, "From");
}

std::string SipRtpCorrelator::extractToUri(const std::string& message) {
    return SipSdpParser::extractHeader(message, "To");
}

uint32_t SipRtpCorrelator::extractCSeq(const std::string& message) {
    std::string cseq_str = SipSdpParser::extractHeader(message, "CSeq");
    if (!cseq_str.empty()) {
        std::istringstream iss(cseq_str);
        uint32_t cseq;
        iss >> cseq;
        return cseq;
    }
    return 0;
}

bool SipRtpCorrelator::parseSdp(const std::string& sdp, std::vector<SdpMedia>& media_list) {
    std::istringstream iss(sdp);
    std::string line;
    SdpMedia current_media;
    bool in_media = false;
    
    while (std::getline(iss, line)) {
        if (line.empty()) continue;
        
        char type = line[0];
        if (line.length() < 2 || line[1] != '=') continue;
        
        std::string value = line.substr(2);
        
        switch (type) {
            case 'm': // Media line
                if (in_media) {
                    media_list.push_back(current_media);
                }
                current_media = SdpMedia();
                parseSdpMediaLine(value, current_media);
                in_media = true;
                break;
                
            case 'a': // Attribute line
                if (in_media) {
                    parseSdpAttributeLine(value, current_media);
                }
                break;
        }
    }
    
    if (in_media) {
        media_list.push_back(current_media);
    }
    
    return !media_list.empty();
}

bool SipRtpCorrelator::parseSdpMediaLine(const std::string& line, SdpMedia& media) {
    return SipSdpParser::parseMediaLine(line, media.media_type, media.port, 
                                       media.protocol, media.payload_types);
}

bool SipRtpCorrelator::parseSdpAttributeLine(const std::string& line, SdpMedia& media) {
    std::string attribute, value;
    if (!SipSdpParser::parseAttributeLine(line, attribute, value)) {
        return false;
    }
    
    if (attribute == "rtpmap") {
        uint8_t pt;
        std::string codec_name;
        uint32_t sample_rate;
        uint16_t channels;
        
        if (SipSdpParser::parseRtpMap(value, pt, codec_name, sample_rate, channels)) {
            media.codecs[pt] = codec_name;
            if (media.media_type == "audio") {
                media.sample_rate = sample_rate;
                media.channels = channels;
            }
        }
    }
    
    return true;
}

MediaType SipRtpCorrelator::getMediaTypeFromPayloadType(uint8_t pt) {
    // Standard audio payload types
    if (pt <= 34 && pt != 25 && pt != 26 && pt != 28 && 
        pt != 31 && pt != 32 && pt != 33 && pt != 34) {
        return MediaType::AUDIO;
    }
    
    // Standard video payload types
    if (pt == 25 || pt == 26 || pt == 28 || pt == 31 || 
        pt == 32 || pt == 33 || pt == 34) {
        return MediaType::VIDEO;
    }
    
    return MediaType::UNKNOWN;
}

std::string SipRtpCorrelator::getCodecNameFromPayloadType(uint8_t pt) {
    return rtp::RtpEngine::getPayloadTypeName(pt);
}

RtpCorrelation* SipRtpCorrelator::getCorrelation(uint32_t ssrc) {
    auto it = correlations_.find(ssrc);
    return (it != correlations_.end()) ? &it->second : nullptr;
}

std::vector<RtpCorrelation> SipRtpCorrelator::getAllCorrelations() const {
    std::vector<RtpCorrelation> result;
    for (const auto& pair : correlations_) {
        result.push_back(pair.second);
    }
    return result;
}

SipCallInfo* SipRtpCorrelator::getCallInfo(const std::string& call_id) {
    auto it = call_info_.find(call_id);
    return (it != call_info_.end()) ? &it->second : nullptr;
}

bool SipRtpCorrelator::restoreCall(const std::string& call_id, const std::string& output_dir) {
    // Find all streams for this call
    auto streams_it = call_streams_.find(call_id);
    if (streams_it == call_streams_.end()) {
        return false;
    }
    
    std::vector<rtp::RtpStream*> audio_streams;
    std::vector<rtp::RtpStream*> video_streams;
    
    for (uint32_t ssrc : streams_it->second) {
        rtp::RtpStream* stream = rtp_engine_.getStream(ssrc);
        if (stream) {
            if (stream->isAudio()) {
                audio_streams.push_back(stream);
            } else if (stream->isVideo()) {
                video_streams.push_back(stream);
            }
        }
    }
    
    // Restore audio streams
    if (!audio_streams.empty()) {
        RestorationConfig config;
        config.output_directory = output_dir;
        config.format = OutputFormat::WAV;
        config.merge_streams = true;
        
        MediaRestorer restorer(config);
        std::string audio_file = output_dir + "/" + call_id + "_audio.wav";
        
        if (restorer.restoreAudioToWav(audio_streams, audio_file)) {
            std::cout << "Restored audio for call " << call_id << " to " << audio_file << std::endl;
        }
    }
    
    // Video restoration would go here
    
    return true;
}

// SipSdpParser implementation
std::string SipSdpParser::extractHeader(const std::string& message,
                                       const std::string& header_name) {
    std::istringstream iss(message);
    std::string line;

    while (std::getline(iss, line)) {
        // Remove carriage return if present
        if (!line.empty() && line.back() == '\r') {
            line.pop_back();
        }

        if (line.empty()) {
            break; // End of headers
        }

        // Check for header match (case insensitive)
        std::string lower_line = line;
        std::transform(lower_line.begin(), lower_line.end(), lower_line.begin(), ::tolower);
        std::string lower_header = header_name;
        std::transform(lower_header.begin(), lower_header.end(), lower_header.begin(), ::tolower);

        if (lower_line.find(lower_header + ":") == 0) {
            size_t colon_pos = line.find(':');
            if (colon_pos != std::string::npos) {
                std::string value = line.substr(colon_pos + 1);
                // Trim leading whitespace
                value.erase(0, value.find_first_not_of(" \t"));
                return value;
            }
        }
    }

    return "";
}

std::string SipSdpParser::extractSdp(const std::string& message) {
    size_t body_start = message.find("\r\n\r\n");
    if (body_start == std::string::npos) {
        body_start = message.find("\n\n");
        if (body_start == std::string::npos) {
            return "";
        }
        body_start += 2;
    } else {
        body_start += 4;
    }

    if (body_start >= message.length()) {
        return "";
    }

    return message.substr(body_start);
}

bool SipSdpParser::parseConnectionLine(const std::string& line,
                                     std::string& network_type,
                                     std::string& address_type,
                                     std::string& connection_address) {
    std::istringstream iss(line);
    return static_cast<bool>(iss >> network_type >> address_type >> connection_address);
}

bool SipSdpParser::parseMediaLine(const std::string& line,
                                std::string& media_type,
                                uint16_t& port,
                                std::string& protocol,
                                std::vector<uint8_t>& payload_types) {
    std::istringstream iss(line);

    if (!(iss >> media_type >> port >> protocol)) {
        return false;
    }

    // Parse payload types
    std::string pt_str;
    while (iss >> pt_str) {
        try {
            uint8_t pt = static_cast<uint8_t>(std::stoi(pt_str));
            payload_types.push_back(pt);
        } catch (const std::exception&) {
            // Ignore invalid payload types
        }
    }

    return true;
}

bool SipSdpParser::parseAttributeLine(const std::string& line,
                                    std::string& attribute,
                                    std::string& value) {
    size_t colon_pos = line.find(':');
    if (colon_pos == std::string::npos) {
        attribute = line;
        value = "";
    } else {
        attribute = line.substr(0, colon_pos);
        value = line.substr(colon_pos + 1);
    }

    return true;
}

bool SipSdpParser::parseRtpMap(const std::string& value,
                             uint8_t& payload_type,
                             std::string& codec_name,
                             uint32_t& sample_rate,
                             uint16_t& channels) {
    // Format: "96 opus/48000/2" or "0 PCMU/8000"
    std::istringstream iss(value);
    std::string pt_str, codec_info;

    if (!(iss >> pt_str >> codec_info)) {
        return false;
    }

    try {
        payload_type = static_cast<uint8_t>(std::stoi(pt_str));
    } catch (const std::exception&) {
        return false;
    }

    // Parse codec info: "codec/rate" or "codec/rate/channels"
    size_t slash1 = codec_info.find('/');
    if (slash1 == std::string::npos) {
        return false;
    }

    codec_name = codec_info.substr(0, slash1);

    size_t slash2 = codec_info.find('/', slash1 + 1);
    std::string rate_str;
    std::string channels_str = "1"; // Default to mono

    if (slash2 == std::string::npos) {
        rate_str = codec_info.substr(slash1 + 1);
    } else {
        rate_str = codec_info.substr(slash1 + 1, slash2 - slash1 - 1);
        channels_str = codec_info.substr(slash2 + 1);
    }

    try {
        sample_rate = static_cast<uint32_t>(std::stoi(rate_str));
        channels = static_cast<uint16_t>(std::stoi(channels_str));
    } catch (const std::exception&) {
        return false;
    }

    return true;
}

} // namespace media
