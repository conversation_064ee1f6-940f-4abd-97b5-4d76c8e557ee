#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>

#define PROTO_NAME      "rtcp"
#define RTCP_MIN_LEN    8

// RTCP Packet Types (RFC 3550)
#define RTCP_SR   200  // Sender Report
#define RTCP_RR   201  // Receiver Report  
#define RTCP_SDES 202  // Source Description
#define RTCP_BYE  203  // Goodbye
#define RTCP_APP  204  // Application-defined

// RTCP Common Header
typedef struct {
    uint8_t  version:2;    // Version: 2 bits
    uint8_t  padding:1;    // Padding: 1 bit
    uint8_t  rc:5;         // Reception report count: 5 bits
    uint8_t  pt;           // Packet type: 8 bits
    uint16_t length;       // Length: 16 bits
} rtcp_header_t;

static
int rtcp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Check minimum RTCP header length
    if (nxt_mbuf_get_length(mbuf) < RTCP_MIN_LEN) {
        return -1;
    }

    // Parse RTCP header
    uint8_t first_byte = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t pt = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    
    uint8_t version = (first_byte >> 6) & 0x03;
    uint8_t padding = (first_byte >> 5) & 0x01;
    uint8_t rc = first_byte & 0x1F;

    // Store common header fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "padding", uinteger, padding);
    precord_put(precord, "rc",      uinteger, rc);
    precord_put(precord, "pt",      uinteger, pt);
    precord_put(precord, "length",  uinteger, length);

    // Calculate packet length in bytes (length field is in 32-bit words minus 1)
    int packet_len = (length + 1) * 4;
    
    if (nxt_mbuf_get_length(mbuf) < packet_len) {
        return -1;
    }

    // Parse specific packet types
    switch (pt) {
        case RTCP_SR: {
            // Sender Report
            if (packet_len >= 28) {  // Minimum SR length
                uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
                uint32_t ntp_msw = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                uint32_t ntp_lsw = nxt_mbuf_get_uint32_ntoh(mbuf, 12);
                uint32_t rtp_timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 16);
                uint32_t packet_count = nxt_mbuf_get_uint32_ntoh(mbuf, 20);
                uint32_t octet_count = nxt_mbuf_get_uint32_ntoh(mbuf, 24);
                
                precord_put(precord, "sr_ssrc",         uinteger, ssrc);
                precord_put(precord, "sr_ntp_msw",      uinteger, ntp_msw);
                precord_put(precord, "sr_ntp_lsw",      uinteger, ntp_lsw);
                precord_put(precord, "sr_rtp_timestamp", uinteger, rtp_timestamp);
                precord_put(precord, "sr_packet_count", uinteger, packet_count);
                precord_put(precord, "sr_octet_count",  uinteger, octet_count);
            }
            break;
        }
        case RTCP_RR: {
            // Receiver Report
            if (packet_len >= 8) {
                uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
                precord_put(precord, "rr_ssrc", uinteger, ssrc);
            }
            break;
        }
        case RTCP_SDES: {
            // Source Description
            if (packet_len >= 8) {
                uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
                precord_put(precord, "sdes_ssrc", uinteger, ssrc);
            }
            break;
        }
        case RTCP_BYE: {
            // Goodbye
            if (packet_len >= 8) {
                uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
                precord_put(precord, "bye_ssrc", uinteger, ssrc);
            }
            break;
        }
        case RTCP_APP: {
            // Application-defined
            if (packet_len >= 12) {
                uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
                uint32_t name = nxt_mbuf_get_uint32_ntoh(mbuf, 8);
                precord_put(precord, "app_ssrc", uinteger, ssrc);
                precord_put(precord, "app_name", uinteger, name);
            }
            break;
        }
    }

    return packet_len;
}

static
int rtcp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "rtcp");
    
    // Common header fields
    pschema_register_field(pschema, "version", YA_FT_UINT8,  "rtcp version");
    pschema_register_field(pschema, "padding", YA_FT_UINT8,  "rtcp padding");
    pschema_register_field(pschema, "rc",      YA_FT_UINT8,  "rtcp reception report count");
    pschema_register_field(pschema, "pt",      YA_FT_UINT8,  "rtcp packet type");
    pschema_register_field(pschema, "length",  YA_FT_UINT16, "rtcp length");
    
    // Sender Report fields
    pschema_register_field(pschema, "sr_ssrc",          YA_FT_UINT32, "sr ssrc");
    pschema_register_field(pschema, "sr_ntp_msw",       YA_FT_UINT32, "sr ntp timestamp msw");
    pschema_register_field(pschema, "sr_ntp_lsw",       YA_FT_UINT32, "sr ntp timestamp lsw");
    pschema_register_field(pschema, "sr_rtp_timestamp", YA_FT_UINT32, "sr rtp timestamp");
    pschema_register_field(pschema, "sr_packet_count",  YA_FT_UINT32, "sr packet count");
    pschema_register_field(pschema, "sr_octet_count",   YA_FT_UINT32, "sr octet count");
    
    // Receiver Report fields
    pschema_register_field(pschema, "rr_ssrc", YA_FT_UINT32, "rr ssrc");
    
    // Source Description fields
    pschema_register_field(pschema, "sdes_ssrc", YA_FT_UINT32, "sdes ssrc");
    
    // Goodbye fields
    pschema_register_field(pschema, "bye_ssrc", YA_FT_UINT32, "bye ssrc");
    
    // Application-defined fields
    pschema_register_field(pschema, "app_ssrc", YA_FT_UINT32, "app ssrc");
    pschema_register_field(pschema, "app_name", YA_FT_UINT32, "app name");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rtcp",
    .schemaRegFun = rtcp_schema_reg,
    .dissectFun   = rtcp_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 8001),  // Common RTCP port
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rtcp)
{
    nxt_dissector_register(&gDissectorDef);
}
