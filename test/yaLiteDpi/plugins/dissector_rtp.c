#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>

#define PROTO_NAME     "rtp"
#define HEADER_LEN_RTP 12

// RTP Header structure (RFC 3550)
typedef struct {
    uint8_t  version:2;      // Version (V): 2 bits
    uint8_t  padding:1;      // Padding (P): 1 bit
    uint8_t  extension:1;    // Extension (X): 1 bit
    uint8_t  cc:4;           // CSRC count (CC): 4 bits
    uint8_t  marker:1;       // Marker (M): 1 bit
    uint8_t  pt:7;           // Payload type (PT): 7 bits
    uint16_t seq;            // Sequence number: 16 bits
    uint32_t timestamp;      // Timestamp: 32 bits
    uint32_t ssrc;           // SSRC: 32 bits
} rtp_header_t;

static
int rtp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Check minimum RTP header length
    if (nxt_mbuf_get_length(mbuf) < HEADER_LEN_RTP) {
        return -1;
    }

    // Parse RTP header fields
    uint8_t first_byte = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t second_byte = nxt_mbuf_get_uint8(mbuf, 1);

    uint8_t version = (first_byte >> 6) & 0x03;
    uint8_t padding = (first_byte >> 5) & 0x01;
    uint8_t extension = (first_byte >> 4) & 0x01;
    uint8_t cc = first_byte & 0x0F;

    uint8_t marker = (second_byte >> 7) & 0x01;
    uint8_t pt = second_byte & 0x7F;

    uint16_t seq = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint32_t timestamp = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
    uint32_t ssrc = nxt_mbuf_get_uint32_ntoh(mbuf, 8);

    // Store parsed fields
    precord_put(precord, "version",   uinteger, version);
    precord_put(precord, "padding",   uinteger, padding);
    precord_put(precord, "extension", uinteger, extension);
    precord_put(precord, "cc",        uinteger, cc);
    precord_put(precord, "marker",    uinteger, marker);
    precord_put(precord, "p_type",    uinteger, pt);
    precord_put(precord, "seq",       uinteger, seq);
    precord_put(precord, "timestamp", uinteger, timestamp);
    precord_put(precord, "ssrc",      uinteger, ssrc);

    // Calculate header length including CSRC list
    int header_len = HEADER_LEN_RTP + (cc * 4);

    // Handle extension header if present
    if (extension && nxt_mbuf_get_length(mbuf) >= header_len + 4) {
        uint16_t ext_profile = nxt_mbuf_get_uint16_ntoh(mbuf, header_len);
        uint16_t ext_length = nxt_mbuf_get_uint16_ntoh(mbuf, header_len + 2);
        precord_put(precord, "ext_profile", uinteger, ext_profile);
        precord_put(precord, "ext_length",  uinteger, ext_length);
        header_len += 4 + (ext_length * 4);
    }

    // Store payload information
    int payload_len = nxt_mbuf_get_length(mbuf) - header_len;
    if (payload_len > 0) {
        precord_put(precord, "payload_len", uinteger, payload_len);
        // Store first few bytes of payload for analysis
        int sample_len = payload_len > 16 ? 16 : payload_len;
        precord_put(precord, "payload_sample", bytes, nxt_mbuf_get_raw(mbuf, header_len), sample_len);
    }

    return header_len;
}

static
int rtp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "rtp");

     // RTP header fields
     pschema_register_field(pschema, "version",      YA_FT_UINT8,  "rtp version");
     pschema_register_field(pschema, "padding",      YA_FT_UINT8,  "rtp padding");
     pschema_register_field(pschema, "extension",    YA_FT_UINT8,  "rtp extension");
     pschema_register_field(pschema, "cc",           YA_FT_UINT8,  "rtp csrc count");
     pschema_register_field(pschema, "marker",       YA_FT_UINT8,  "rtp marker");
     pschema_register_field(pschema, "p_type",       YA_FT_UINT8,  "rtp payload type");
     pschema_register_field(pschema, "seq",          YA_FT_UINT16, "rtp sequence number");
     pschema_register_field(pschema, "timestamp",    YA_FT_UINT32, "rtp timestamp");
     pschema_register_field(pschema, "ssrc",         YA_FT_UINT32, "rtp ssrc");

     // Extension fields
     pschema_register_field(pschema, "ext_profile",  YA_FT_UINT16, "rtp extension profile");
     pschema_register_field(pschema, "ext_length",   YA_FT_UINT16, "rtp extension length");

     // Payload fields
     pschema_register_field(pschema, "payload_len",    YA_FT_UINT32, "rtp payload length");
     pschema_register_field(pschema, "payload_sample", YA_FT_BYTES,  "rtp payload sample");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rtp",
    .schemaRegFun = rtp_schema_reg,
    .dissectFun   = rtp_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 8000),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rtp)
{
    nxt_dissector_register(&gDissectorDef);
}
