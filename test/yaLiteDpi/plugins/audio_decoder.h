#ifndef AUDIO_DECODER_H
#define AUDIO_DECODER_H

#include <cstdint>
#include <vector>
#include <memory>
#include <string>

namespace media {

// Audio codec types
enum class AudioCodec {
    UNKNOWN = 0,
    G711_ULAW = 0,   // G.711 μ-law (PCMU)
    G711_ALAW = 8,   // G.711 A-law (PCMA)
    G722 = 9,        // G.722
    G723 = 4,        // G.723.1
    G726 = 2,        // G.726 (DVI4)
    G728 = 15,       // G.728
    G729 = 18,       // G.729
    GSM = 3,         // GSM 06.10
    OPUS = 96,       // Opus (dynamic)
    AMR = 97,        // AMR (dynamic)
    L16_MONO = 11,   // L16 mono
    L16_STEREO = 10  // L16 stereo
};

// Audio frame structure
struct AudioFrame {
    std::vector<uint8_t> data;
    uint32_t timestamp;
    uint32_t sample_rate;
    uint16_t channels;
    uint16_t bits_per_sample;
    uint32_t samples_per_frame;
    
    AudioFrame() : timestamp(0), sample_rate(8000), channels(1), 
                  bits_per_sample(16), samples_per_frame(0) {}
};

// PCM audio data
struct PcmData {
    std::vector<int16_t> samples;
    uint32_t sample_rate;
    uint16_t channels;
    uint32_t timestamp;
    
    PcmData() : sample_rate(8000), channels(1), timestamp(0) {}
};

// Base audio decoder interface
class AudioDecoder {
public:
    virtual ~AudioDecoder() = default;
    
    // Initialize decoder with codec parameters
    virtual bool initialize(uint32_t sample_rate, uint16_t channels) = 0;
    
    // Decode audio frame to PCM
    virtual bool decode(const AudioFrame& frame, PcmData& pcm) = 0;
    
    // Get codec information
    virtual AudioCodec getCodec() const = 0;
    virtual std::string getCodecName() const = 0;
    
    // Cleanup
    virtual void cleanup() = 0;
};

// G.711 μ-law decoder
class G711UlawDecoder : public AudioDecoder {
public:
    bool initialize(uint32_t sample_rate, uint16_t channels) override;
    bool decode(const AudioFrame& frame, PcmData& pcm) override;
    AudioCodec getCodec() const override { return AudioCodec::G711_ULAW; }
    std::string getCodecName() const override { return "G.711 μ-law"; }
    void cleanup() override;

private:
    uint32_t sample_rate_;
    uint16_t channels_;
    static int16_t ulaw_to_linear(uint8_t ulaw);
};

// G.711 A-law decoder
class G711AlawDecoder : public AudioDecoder {
public:
    bool initialize(uint32_t sample_rate, uint16_t channels) override;
    bool decode(const AudioFrame& frame, PcmData& pcm) override;
    AudioCodec getCodec() const override { return AudioCodec::G711_ALAW; }
    std::string getCodecName() const override { return "G.711 A-law"; }
    void cleanup() override;

private:
    uint32_t sample_rate_;
    uint16_t channels_;
    static int16_t alaw_to_linear(uint8_t alaw);
};

// G.722 decoder
class G722Decoder : public AudioDecoder {
public:
    G722Decoder();
    ~G722Decoder();
    
    bool initialize(uint32_t sample_rate, uint16_t channels) override;
    bool decode(const AudioFrame& frame, PcmData& pcm) override;
    AudioCodec getCodec() const override { return AudioCodec::G722; }
    std::string getCodecName() const override { return "G.722"; }
    void cleanup() override;

private:
    struct G722State;
    G722State* state_;
    uint32_t sample_rate_;
    uint16_t channels_;
};

// L16 decoder (uncompressed linear PCM)
class L16Decoder : public AudioDecoder {
public:
    bool initialize(uint32_t sample_rate, uint16_t channels) override;
    bool decode(const AudioFrame& frame, PcmData& pcm) override;
    AudioCodec getCodec() const override { return channels_ == 1 ? AudioCodec::L16_MONO : AudioCodec::L16_STEREO; }
    std::string getCodecName() const override { return "L16"; }
    void cleanup() override;

private:
    uint32_t sample_rate_;
    uint16_t channels_;
};

// Audio decoder factory
class AudioDecoderFactory {
public:
    static std::unique_ptr<AudioDecoder> createDecoder(AudioCodec codec);
    static std::unique_ptr<AudioDecoder> createDecoder(uint8_t payload_type);
    static AudioCodec getCodecFromPayloadType(uint8_t payload_type);
    static std::string getCodecName(AudioCodec codec);
};

} // namespace media

#endif // AUDIO_DECODER_H
