#ifndef SESSION_MANAGER_H
#define SESSION_MANAGER_H

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>

namespace media {

// Media types
enum class MediaType {
    UNKNOWN = 0,
    AUDIO = 1,
    VIDEO = 2
};

// Session states
enum class SessionState {
    SETUP = 0,      // Session being set up
    ACTIVE = 1,     // Session active with media flow
    TEARDOWN = 2,   // Session being torn down
    TERMINATED = 3  // Session terminated
};

// RTP Stream information
struct RtpStreamInfo {
    uint32_t ssrc;
    MediaType media_type;
    uint8_t payload_type;
    std::string codec_name;
    uint32_t sample_rate;
    uint16_t channels;
    
    // Network information
    std::string src_ip;
    std::string dst_ip;
    uint16_t src_port;
    uint16_t dst_port;
    
    // Statistics
    uint32_t packet_count;
    uint64_t byte_count;
    uint32_t lost_packets;
    
    RtpStreamInfo() : ssrc(0), media_type(MediaType::UNKNOWN), 
                     payload_type(0), sample_rate(0), channels(0),
                     src_port(0), dst_port(0), packet_count(0), 
                     byte_count(0), lost_packets(0) {}
};

// Session statistics
struct SessionStats {
    uint32_t session_id;
    std::string call_id;
    std::string protocol;
    SessionState state;
    std::chrono::system_clock::time_point creation_time;
    uint32_t stream_count;
    uint32_t audio_stream_count;
    uint32_t video_stream_count;
    
    SessionStats() : session_id(0), state(SessionState::SETUP),
                    stream_count(0), audio_stream_count(0), 
                    video_stream_count(0) {}
};

// Forward declaration
class MediaSession;

// Session Manager - manages media sessions
class SessionManager {
public:
    SessionManager();
    ~SessionManager();
    
    // Session management
    uint32_t createSession(const std::string& call_id, const std::string& protocol);
    MediaSession* getSession(uint32_t session_id);
    MediaSession* getSessionByCallId(const std::string& call_id);
    bool removeSession(uint32_t session_id);
    std::vector<MediaSession*> getAllSessions();
    
    // RTP stream management
    bool addRtpStream(uint32_t session_id, uint32_t ssrc, const RtpStreamInfo& stream_info);
    
private:
    std::map<uint32_t, MediaSession*> sessions_;           // session_id -> session
    std::map<std::string, uint32_t> call_id_to_session_;   // call_id -> session_id
    uint32_t next_session_id_;
    std::mutex mutex_;
};

// Media Session - represents a single media session (call)
class MediaSession {
public:
    MediaSession(uint32_t id, const std::string& call_id, const std::string& protocol);
    ~MediaSession();
    
    // RTP stream management
    bool addRtpStream(uint32_t ssrc, const RtpStreamInfo& stream_info);
    bool removeRtpStream(uint32_t ssrc);
    RtpStreamInfo* getRtpStream(uint32_t ssrc);
    std::vector<RtpStreamInfo> getAllRtpStreams() const;
    std::vector<RtpStreamInfo> getAudioStreams() const;
    std::vector<RtpStreamInfo> getVideoStreams() const;
    
    // Session state management
    void setState(SessionState state);
    SessionState getState() const;
    
    // SDP information
    void addSdpInfo(const std::string& sdp);
    std::string getSdpInfo() const;
    
    // Getters
    uint32_t getId() const { return id_; }
    const std::string& getCallId() const { return call_id_; }
    const std::string& getProtocol() const { return protocol_; }
    
    // Statistics
    SessionStats getStats() const;
    
    // Media type detection
    bool hasAudio() const;
    bool hasVideo() const;
    
private:
    uint32_t id_;
    std::string call_id_;
    std::string protocol_;
    SessionState state_;
    std::chrono::system_clock::time_point creation_time_;
    
    std::map<uint32_t, RtpStreamInfo> rtp_streams_;  // ssrc -> stream_info
    std::string sdp_info_;
    
    mutable std::mutex mutex_;
};

} // namespace media

#endif // SESSION_MANAGER_H
