#include "rtp_engine.h"
#include <algorithm>
#include <chrono>
#include <cstring>

namespace rtp {

// RTP Payload Types (RFC 3551)
const std::map<uint8_t, std::string> RtpEngine::payload_type_names = {
    {0,  "PCMU"},     // G.711 μ-law
    {3,  "GSM"},      // GSM 06.10
    {4,  "G723"},     // G.723.1
    {5,  "DVI4_8K"},  // DVI4 8kHz
    {6,  "DVI4_16K"}, // DVI4 16kHz
    {7,  "LPC"},      // LPC
    {8,  "PCMA"},     // G.711 A-law
    {9,  "G722"},     // G.722
    {10, "L16_2CH"},  // L16 stereo
    {11, "L16_1CH"},  // L16 mono
    {12, "QCELP"},    // QCELP
    {13, "CN"},       // Comfort Noise
    {14, "MPA"},      // MPEG Audio
    {15, "G728"},     // G.728
    {16, "DVI4_11K"}, // DVI4 11.025kHz
    {17, "DVI4_22K"}, // DVI4 22.05kHz
    {18, "G729"},     // G.729
    {25, "CelB"},     // CelB video
    {26, "JPEG"},     // JPEG video
    {28, "nv"},       // nv video
    {31, "H261"},     // H.261 video
    {32, "MPV"},      // MPEG Video
    {33, "MP2T"},     // MPEG-2 Transport
    {34, "H263"},     // H.263 video
    {96, "dynamic"},  // Dynamic payload type start
};

RtpEngine::RtpEngine() : next_stream_id_(1) {
}

RtpEngine::~RtpEngine() {
    for (auto& pair : streams_) {
        delete pair.second;
    }
}

uint32_t RtpEngine::processRtpPacket(const RtpPacket& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Find or create stream
    RtpStream* stream = findOrCreateStream(packet.ssrc, packet.payload_type);
    if (!stream) {
        return 0;
    }
    
    // Add packet to stream
    stream->addPacket(packet);
    
    return stream->getId();
}

RtpStream* RtpEngine::findOrCreateStream(uint32_t ssrc, uint8_t payload_type) {
    auto it = streams_.find(ssrc);
    if (it != streams_.end()) {
        return it->second;
    }
    
    // Create new stream
    RtpStream* stream = new RtpStream(next_stream_id_++, ssrc, payload_type);
    streams_[ssrc] = stream;
    
    return stream;
}

RtpStream* RtpEngine::getStream(uint32_t ssrc) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = streams_.find(ssrc);
    return (it != streams_.end()) ? it->second : nullptr;
}

std::vector<RtpStream*> RtpEngine::getAllStreams() {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<RtpStream*> result;
    for (auto& pair : streams_) {
        result.push_back(pair.second);
    }
    return result;
}

std::string RtpEngine::getPayloadTypeName(uint8_t pt) {
    auto it = payload_type_names.find(pt);
    if (it != payload_type_names.end()) {
        return it->second;
    }
    return "unknown";
}

// RtpStream implementation
RtpStream::RtpStream(uint32_t id, uint32_t ssrc, uint8_t payload_type)
    : id_(id), ssrc_(ssrc), payload_type_(payload_type), 
      packet_count_(0), byte_count_(0), last_seq_(0), 
      seq_initialized_(false), lost_packets_(0) {
}

RtpStream::~RtpStream() {
    for (auto& packet : packets_) {
        delete[] packet.payload;
    }
}

void RtpStream::addPacket(const RtpPacket& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Update statistics
    packet_count_++;
    byte_count_ += packet.payload_length;
    
    // Check for lost packets
    if (seq_initialized_) {
        uint16_t expected_seq = last_seq_ + 1;
        if (packet.sequence != expected_seq) {
            // Handle sequence number wrap-around
            uint16_t diff = packet.sequence - expected_seq;
            if (diff < 32768) {  // Forward jump
                lost_packets_ += diff;
            }
            // Backward jump or large forward jump - ignore for now
        }
    } else {
        seq_initialized_ = true;
    }
    last_seq_ = packet.sequence;
    
    // Store packet (make a copy of payload)
    RtpPacket stored_packet = packet;
    if (packet.payload_length > 0) {
        stored_packet.payload = new uint8_t[packet.payload_length];
        std::memcpy(stored_packet.payload, packet.payload, packet.payload_length);
    }
    
    packets_.push_back(stored_packet);
    
    // Sort packets by sequence number for reordering
    std::sort(packets_.begin(), packets_.end(), 
              [](const RtpPacket& a, const RtpPacket& b) {
                  return a.sequence < b.sequence;
              });
}

std::vector<RtpPacket> RtpStream::getPackets() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return packets_;
}

RtpStreamStats RtpStream::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    RtpStreamStats stats;
    stats.stream_id = id_;
    stats.ssrc = ssrc_;
    stats.payload_type = payload_type_;
    stats.packet_count = packet_count_;
    stats.byte_count = byte_count_;
    stats.lost_packets = lost_packets_;
    
    if (!packets_.empty()) {
        stats.first_timestamp = packets_.front().timestamp;
        stats.last_timestamp = packets_.back().timestamp;
        stats.first_sequence = packets_.front().sequence;
        stats.last_sequence = packets_.back().sequence;
    }
    
    return stats;
}

bool RtpStream::isAudio() const {
    // Audio payload types (simplified check)
    return payload_type_ <= 34 && payload_type_ != 25 && 
           payload_type_ != 26 && payload_type_ != 28 && 
           payload_type_ != 31 && payload_type_ != 32 && 
           payload_type_ != 33 && payload_type_ != 34;
}

bool RtpStream::isVideo() const {
    // Video payload types
    return payload_type_ == 25 || payload_type_ == 26 || 
           payload_type_ == 28 || payload_type_ == 31 || 
           payload_type_ == 32 || payload_type_ == 33 || 
           payload_type_ == 34;
}

} // namespace rtp
