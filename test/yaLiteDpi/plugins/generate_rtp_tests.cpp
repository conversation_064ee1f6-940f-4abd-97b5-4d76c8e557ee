#include "rtp_test_generator.h"
#include <iostream>
#include <string>

int main(int argc, char* argv[]) {
    std::string pcap_dir = "test/pcaps/media_pcaps";
    std::string output_dir = "test/yaLiteDpi/plugins/rtp_tests";
    
    // Parse command line arguments
    if (argc > 1) {
        pcap_dir = argv[1];
    }
    if (argc > 2) {
        output_dir = argv[2];
    }
    
    std::cout << "RTP Test Generator" << std::endl;
    std::cout << "=================" << std::endl;
    std::cout << "PCAP Directory: " << pcap_dir << std::endl;
    std::cout << "Output Directory: " << output_dir << std::endl;
    std::cout << std::endl;
    
    // Create test generator
    media::RtpTestGenerator generator(pcap_dir, output_dir);
    
    // Generate all test cases
    if (generator.generateAllTests()) {
        std::cout << std::endl;
        std::cout << "✅ All test cases generated successfully!" << std::endl;
        std::cout << "Test cases are available in: " << output_dir << std::endl;
        std::cout << std::endl;
        std::cout << "To run all tests:" << std::endl;
        std::cout << "  cd " << output_dir << std::endl;
        std::cout << "  ./run_all_tests.sh" << std::endl;
        std::cout << std::endl;
        std::cout << "To run individual tests:" << std::endl;
        std::cout << "  cd " << output_dir << "/<test_name>" << std::endl;
        std::cout << "  ./run_test.sh" << std::endl;
        
        return 0;
    } else {
        std::cerr << "❌ Failed to generate some test cases" << std::endl;
        return 1;
    }
}
