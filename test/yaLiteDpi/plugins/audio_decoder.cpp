#include "audio_decoder.h"
#include <cstring>
#include <algorithm>

namespace media {

// G.711 μ-law to linear conversion table
static const int16_t ulaw_to_linear_table[256] = {
    -32124, -31100, -30076, -29052, -28028, -27004, -25980, -24956,
    -23932, -22908, -21884, -20860, -19836, -18812, -17788, -16764,
    -15996, -15484, -14972, -14460, -13948, -13436, -12924, -12412,
    -11900, -11388, -10876, -10364,  -9852,  -9340,  -8828,  -8316,
     -7932,  -7676,  -7420,  -7164,  -6908,  -6652,  -6396,  -6140,
     -5884,  -5628,  -5372,  -5116,  -4860,  -4604,  -4348,  -4092,
     -3900,  -3772,  -3644,  -3516,  -3388,  -3260,  -3132,  -3004,
     -2876,  -2748,  -2620,  -2492,  -2364,  -2236,  -2108,  -1980,
     -1884,  -1820,  -1756,  -1692,  -1628,  -1564,  -1500,  -1436,
     -1372,  -1308,  -1244,  -1180,  -1116,  -1052,   -988,   -924,
      -876,   -844,   -812,   -780,   -748,   -716,   -684,   -652,
      -620,   -588,   -556,   -524,   -492,   -460,   -428,   -396,
      -372,   -356,   -340,   -324,   -308,   -292,   -276,   -260,
      -244,   -228,   -212,   -196,   -180,   -164,   -148,   -132,
      -120,   -112,   -104,    -96,    -88,    -80,    -72,    -64,
       -56,    -48,    -40,    -32,    -24,    -16,     -8,      0,
     32124,  31100,  30076,  29052,  28028,  27004,  25980,  24956,
     23932,  22908,  21884,  20860,  19836,  18812,  17788,  16764,
     15996,  15484,  14972,  14460,  13948,  13436,  12924,  12412,
     11900,  11388,  10876,  10364,   9852,   9340,   8828,   8316,
      7932,   7676,   7420,   7164,   6908,   6652,   6396,   6140,
      5884,   5628,   5372,   5116,   4860,   4604,   4348,   4092,
      3900,   3772,   3644,   3516,   3388,   3260,   3132,   3004,
      2876,   2748,   2620,   2492,   2364,   2236,   2108,   1980,
      1884,   1820,   1756,   1692,   1628,   1564,   1500,   1436,
      1372,   1308,   1244,   1180,   1116,   1052,    988,    924,
       876,    844,    812,    780,    748,    716,    684,    652,
       620,    588,    556,    524,    492,    460,    428,    396,
       372,    356,    340,    324,    308,    292,    276,    260,
       244,    228,    212,    196,    180,    164,    148,    132,
       120,    112,    104,     96,     88,     80,     72,     64,
        56,     48,     40,     32,     24,     16,      8,      0
};

// G.711 A-law to linear conversion table
static const int16_t alaw_to_linear_table[256] = {
     -5504,  -5248,  -6016,  -5760,  -4480,  -4224,  -4992,  -4736,
     -7552,  -7296,  -8064,  -7808,  -6528,  -6272,  -7040,  -6784,
     -2752,  -2624,  -3008,  -2880,  -2240,  -2112,  -2496,  -2368,
     -3776,  -3648,  -4032,  -3904,  -3264,  -3136,  -3520,  -3392,
    -22016, -20992, -24064, -23040, -17920, -16896, -19968, -18944,
    -30208, -29184, -32256, -31232, -26112, -25088, -28160, -27136,
    -11008, -10496, -12032, -11520,  -8960,  -8448,  -9984,  -9472,
    -15104, -14592, -16128, -15616, -13056, -12544, -14080, -13568,
      -344,   -328,   -376,   -360,   -280,   -264,   -312,   -296,
      -472,   -456,   -504,   -488,   -408,   -392,   -440,   -424,
       -88,    -72,   -120,   -104,    -24,     -8,    -56,    -40,
      -216,   -200,   -248,   -232,   -152,   -136,   -184,   -168,
     -1376,  -1312,  -1504,  -1440,  -1120,  -1056,  -1248,  -1184,
     -1888,  -1824,  -2016,  -1952,  -1632,  -1568,  -1760,  -1696,
      -688,   -656,   -752,   -720,   -560,   -528,   -624,   -592,
      -944,   -912,  -1008,   -976,   -816,   -784,   -880,   -848,
      5504,   5248,   6016,   5760,   4480,   4224,   4992,   4736,
      7552,   7296,   8064,   7808,   6528,   6272,   7040,   6784,
      2752,   2624,   3008,   2880,   2240,   2112,   2496,   2368,
      3776,   3648,   4032,   3904,   3264,   3136,   3520,   3392,
     22016,  20992,  24064,  23040,  17920,  16896,  19968,  18944,
     30208,  29184,  32256,  31232,  26112,  25088,  28160,  27136,
     11008,  10496,  12032,  11520,   8960,   8448,   9984,   9472,
     15104,  14592,  16128,  15616,  13056,  12544,  14080,  13568,
       344,    328,    376,    360,    280,    264,    312,    296,
       472,    456,    504,    488,    408,    392,    440,    424,
        88,     72,    120,    104,     24,      8,     56,     40,
       216,    200,    248,    232,    152,    136,    184,    168,
      1376,   1312,   1504,   1440,   1120,   1056,   1248,   1184,
      1888,   1824,   2016,   1952,   1632,   1568,   1760,   1696,
       688,    656,    752,    720,    560,    528,    624,    592,
       944,    912,   1008,    976,    816,    784,    880,    848
};

// G.711 μ-law decoder implementation
bool G711UlawDecoder::initialize(uint32_t sample_rate, uint16_t channels) {
    sample_rate_ = sample_rate;
    channels_ = channels;
    return true;
}

bool G711UlawDecoder::decode(const AudioFrame& frame, PcmData& pcm) {
    if (frame.data.empty()) {
        return false;
    }
    
    pcm.sample_rate = sample_rate_;
    pcm.channels = channels_;
    pcm.timestamp = frame.timestamp;
    pcm.samples.clear();
    pcm.samples.reserve(frame.data.size() * channels_);
    
    for (size_t i = 0; i < frame.data.size(); ++i) {
        int16_t sample = ulaw_to_linear_table[frame.data[i]];
        for (uint16_t ch = 0; ch < channels_; ++ch) {
            pcm.samples.push_back(sample);
        }
    }
    
    return true;
}

void G711UlawDecoder::cleanup() {
    // No cleanup needed for G.711
}

int16_t G711UlawDecoder::ulaw_to_linear(uint8_t ulaw) {
    return ulaw_to_linear_table[ulaw];
}

// G.711 A-law decoder implementation
bool G711AlawDecoder::initialize(uint32_t sample_rate, uint16_t channels) {
    sample_rate_ = sample_rate;
    channels_ = channels;
    return true;
}

bool G711AlawDecoder::decode(const AudioFrame& frame, PcmData& pcm) {
    if (frame.data.empty()) {
        return false;
    }
    
    pcm.sample_rate = sample_rate_;
    pcm.channels = channels_;
    pcm.timestamp = frame.timestamp;
    pcm.samples.clear();
    pcm.samples.reserve(frame.data.size() * channels_);
    
    for (size_t i = 0; i < frame.data.size(); ++i) {
        int16_t sample = alaw_to_linear_table[frame.data[i]];
        for (uint16_t ch = 0; ch < channels_; ++ch) {
            pcm.samples.push_back(sample);
        }
    }
    
    return true;
}

void G711AlawDecoder::cleanup() {
    // No cleanup needed for G.711
}

int16_t G711AlawDecoder::alaw_to_linear(uint8_t alaw) {
    return alaw_to_linear_table[alaw];
}

// L16 decoder implementation
bool L16Decoder::initialize(uint32_t sample_rate, uint16_t channels) {
    sample_rate_ = sample_rate;
    channels_ = channels;
    return true;
}

bool L16Decoder::decode(const AudioFrame& frame, PcmData& pcm) {
    if (frame.data.empty() || frame.data.size() % 2 != 0) {
        return false;
    }
    
    pcm.sample_rate = sample_rate_;
    pcm.channels = channels_;
    pcm.timestamp = frame.timestamp;
    pcm.samples.clear();
    pcm.samples.reserve(frame.data.size() / 2);
    
    // Convert big-endian 16-bit samples to host byte order
    for (size_t i = 0; i < frame.data.size(); i += 2) {
        int16_t sample = (frame.data[i] << 8) | frame.data[i + 1];
        pcm.samples.push_back(sample);
    }
    
    return true;
}

void L16Decoder::cleanup() {
    // No cleanup needed for L16
}

// Audio decoder factory implementation
std::unique_ptr<AudioDecoder> AudioDecoderFactory::createDecoder(AudioCodec codec) {
    switch (codec) {
        case AudioCodec::G711_ULAW:
            return std::make_unique<G711UlawDecoder>();
        case AudioCodec::G711_ALAW:
            return std::make_unique<G711AlawDecoder>();
        case AudioCodec::L16_MONO:
        case AudioCodec::L16_STEREO:
            return std::make_unique<L16Decoder>();
        default:
            return nullptr;
    }
}

std::unique_ptr<AudioDecoder> AudioDecoderFactory::createDecoder(uint8_t payload_type) {
    AudioCodec codec = getCodecFromPayloadType(payload_type);
    return createDecoder(codec);
}

AudioCodec AudioDecoderFactory::getCodecFromPayloadType(uint8_t payload_type) {
    switch (payload_type) {
        case 0:  return AudioCodec::G711_ULAW;
        case 8:  return AudioCodec::G711_ALAW;
        case 9:  return AudioCodec::G722;
        case 4:  return AudioCodec::G723;
        case 15: return AudioCodec::G728;
        case 18: return AudioCodec::G729;
        case 3:  return AudioCodec::GSM;
        case 11: return AudioCodec::L16_MONO;
        case 10: return AudioCodec::L16_STEREO;
        default: return AudioCodec::UNKNOWN;
    }
}

std::string AudioDecoderFactory::getCodecName(AudioCodec codec) {
    switch (codec) {
        case AudioCodec::G711_ULAW: return "G.711 μ-law";
        case AudioCodec::G711_ALAW: return "G.711 A-law";
        case AudioCodec::G722:      return "G.722";
        case AudioCodec::G723:      return "G.723.1";
        case AudioCodec::G726:      return "G.726";
        case AudioCodec::G728:      return "G.728";
        case AudioCodec::G729:      return "G.729";
        case AudioCodec::GSM:       return "GSM 06.10";
        case AudioCodec::OPUS:      return "Opus";
        case AudioCodec::AMR:       return "AMR";
        case AudioCodec::L16_MONO:  return "L16 Mono";
        case AudioCodec::L16_STEREO: return "L16 Stereo";
        default:                    return "Unknown";
    }
}

} // namespace media
