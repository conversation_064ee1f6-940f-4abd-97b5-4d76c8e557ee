#ifndef RTP_TEST_GENERATOR_H
#define RTP_TEST_GENERATOR_H

#include "audio_decoder.h"
#include <string>
#include <vector>

namespace media {

// Test case structure
struct TestCase {
    std::string name;           // Test case name (e.g., "g711a")
    std::string description;    // Human-readable description
    AudioCodec codec;          // Audio codec type
    std::string pcap_file;     // Path to PCAP file
    
    TestCase(const std::string& n, const std::string& desc, 
             AudioCodec c, const std::string& pcap)
        : name(n), description(desc), codec(c), pcap_file(pcap) {}
};

// RTP test generator - creates test cases from PCAP files
class RtpTestGenerator {
public:
    RtpTestGenerator(const std::string& pcap_dir, const std::string& output_dir);
    ~RtpTestGenerator();
    
    // Generate all test cases
    bool generateAllTests();
    
    // Generate specific test case
    bool generateTestCase(const TestCase& test_case);
    
    // Get list of available test cases
    const std::vector<TestCase>& getTestCases() const { return test_cases_; }
    
private:
    std::string pcap_directory_;
    std::string output_directory_;
    std::vector<TestCase> test_cases_;
    
    // Initialize test cases based on available PCAP files
    void initializeTestCases();
    
    // Generate test script for a specific test case
    bool generateTestScript(const TestCase& test_case, const std::string& test_dir);
    
    // Generate expected results documentation
    bool generateExpectedResults(const TestCase& test_case, const std::string& test_dir);
    
    // Generate test summary and master script
    bool generateTestSummary();
    bool generateMasterTestScript();
};

} // namespace media

#endif // RTP_TEST_GENERATOR_H
