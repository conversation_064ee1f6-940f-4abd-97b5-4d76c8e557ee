#include "media_restorer.h"
#include <algorithm>
#include <chrono>
#include <filesystem>
#include <iostream>
#include <cmath>

namespace media {

// JitterBuffer implementation
JitterBuffer::JitterBuffer(uint32_t max_size) 
    : max_size_(max_size), base_seq_(0), seq_initialized_(false) {
    buffer_.resize(max_size_);
}

JitterBuffer::~JitterBuffer() {
}

void JitterBuffer::addPacket(const rtp::RtpPacket& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!seq_initialized_) {
        base_seq_ = packet.sequence;
        seq_initialized_ = true;
    }
    
    uint32_t index = getBufferIndex(packet.sequence);
    if (index < max_size_) {
        buffer_[index].packet = packet;
        buffer_[index].valid = true;
    }
}

bool JitterBuffer::getNextPacket(rtp::RtpPacket& packet) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!seq_initialized_) {
        return false;
    }
    
    uint32_t index = getBufferIndex(base_seq_);
    if (index < max_size_ && buffer_[index].valid) {
        packet = buffer_[index].packet;
        buffer_[index].valid = false;
        base_seq_++;
        return true;
    }
    
    return false;
}

bool JitterBuffer::hasPackets() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!seq_initialized_) {
        return false;
    }
    
    uint32_t index = getBufferIndex(base_seq_);
    return (index < max_size_ && buffer_[index].valid);
}

void JitterBuffer::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& entry : buffer_) {
        entry.valid = false;
    }
    seq_initialized_ = false;
}

uint32_t JitterBuffer::getBufferIndex(uint16_t seq) const {
    if (!seq_initialized_) {
        return max_size_;  // Invalid index
    }
    
    int16_t diff = seq - base_seq_;
    if (diff < 0 || diff >= static_cast<int16_t>(max_size_)) {
        return max_size_;  // Invalid index
    }
    
    return static_cast<uint32_t>(diff);
}

// MediaRestorer implementation
MediaRestorer::MediaRestorer(const RestorationConfig& config) 
    : config_(config) {
    if (config_.enable_jitter_buffer) {
        jitter_buffer_ = std::make_unique<JitterBuffer>(config_.jitter_buffer_size);
    }
    createOutputDirectory();
}

MediaRestorer::~MediaRestorer() {
}

bool MediaRestorer::restoreStream(rtp::RtpStream* stream, const std::string& output_file) {
    if (!stream) {
        return false;
    }
    
    // Reset statistics
    last_stats_ = RestorationStats();
    last_stats_.output_file = output_file;
    
    if (stream->isAudio()) {
        return restoreAudioStream(stream, output_file);
    } else if (stream->isVideo()) {
        // Video restoration not implemented yet
        return false;
    }
    
    return false;
}

bool MediaRestorer::restoreSession(MediaSession* session) {
    if (!session) {
        return false;
    }
    
    std::vector<rtp::RtpStream*> audio_streams;
    // Note: We need to get RTP streams from the session
    // This requires integration with the RTP engine
    
    if (!audio_streams.empty()) {
        std::string output_file = generateOutputFileName(session, "_audio.wav");
        return restoreAudioToWav(audio_streams, output_file);
    }
    
    return false;
}

bool MediaRestorer::restoreAudioToWav(const std::vector<rtp::RtpStream*>& streams, 
                                     const std::string& output_file) {
    if (streams.empty()) {
        return false;
    }
    
    // Reset statistics
    last_stats_ = RestorationStats();
    last_stats_.output_file = output_file;
    
    if (config_.merge_streams && streams.size() > 1) {
        // Decode all streams and merge
        std::vector<std::vector<PcmData>> multi_stream_data;
        for (auto* stream : streams) {
            std::vector<PcmData> pcm_data = decodeRtpStream(stream);
            if (!pcm_data.empty()) {
                multi_stream_data.push_back(pcm_data);
            }
        }
        
        if (!multi_stream_data.empty()) {
            std::vector<PcmData> merged_data;
            mergePcmData(multi_stream_data, merged_data);
            return writeWavFile(merged_data, output_file);
        }
    } else {
        // Process first stream only
        return restoreAudioStream(streams[0], output_file);
    }
    
    return false;
}

bool MediaRestorer::restoreAudioStream(rtp::RtpStream* stream, const std::string& output_file) {
    if (!stream || !stream->isAudio()) {
        return false;
    }
    
    // Decode RTP stream to PCM
    std::vector<PcmData> pcm_data = decodeRtpStream(stream);
    if (pcm_data.empty()) {
        return false;
    }
    
    // Fill gaps with silence if needed
    if (config_.max_gap_ms > 0) {
        fillGaps(pcm_data, pcm_data[0].sample_rate);
    }
    
    // Write to output file
    if (config_.format == OutputFormat::WAV) {
        return writeWavFile(pcm_data, output_file);
    } else if (config_.format == OutputFormat::PCM) {
        return writePcmFile(pcm_data, output_file);
    }
    
    return false;
}

std::vector<PcmData> MediaRestorer::decodeRtpStream(rtp::RtpStream* stream) {
    std::vector<PcmData> pcm_data;
    
    if (!stream) {
        return pcm_data;
    }
    
    // Create decoder for this stream
    auto decoder = AudioDecoderFactory::createDecoder(stream->getPayloadType());
    if (!decoder) {
        std::cerr << "Unsupported audio codec: " << static_cast<int>(stream->getPayloadType()) << std::endl;
        return pcm_data;
    }
    
    // Initialize decoder (assume 8kHz mono for now)
    if (!decoder->initialize(8000, 1)) {
        std::cerr << "Failed to initialize audio decoder" << std::endl;
        return pcm_data;
    }
    
    // Get packets from stream
    std::vector<rtp::RtpPacket> packets = stream->getPackets();
    last_stats_.total_packets = packets.size();
    
    // Process packets
    for (const auto& rtp_packet : packets) {
        if (rtp_packet.payload_length == 0) {
            continue;
        }
        
        // Convert RTP packet to AudioFrame
        AudioFrame frame;
        frame.data.assign(rtp_packet.payload, 
                         rtp_packet.payload + rtp_packet.payload_length);
        frame.timestamp = rtp_packet.timestamp;
        frame.sample_rate = 8000;  // Default, should be determined from SDP
        frame.channels = 1;
        frame.bits_per_sample = 16;
        
        // Decode frame
        PcmData pcm;
        if (decoder->decode(frame, pcm)) {
            pcm_data.push_back(pcm);
            last_stats_.decoded_packets++;
        }
    }
    
    // Calculate duration
    last_stats_.duration_ms = MediaUtils::calculateDurationMs(pcm_data);
    
    decoder->cleanup();
    return pcm_data;
}

bool MediaRestorer::writeWavFile(const std::vector<PcmData>& pcm_data, 
                                const std::string& output_file) {
    if (pcm_data.empty()) {
        return false;
    }
    
    std::ofstream file(output_file, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open output file: " << output_file << std::endl;
        return false;
    }
    
    // Calculate total samples and data size
    uint32_t total_samples = 0;
    uint32_t sample_rate = pcm_data[0].sample_rate;
    uint16_t channels = pcm_data[0].channels;
    
    for (const auto& pcm : pcm_data) {
        total_samples += pcm.samples.size();
    }
    
    uint32_t data_size = total_samples * sizeof(int16_t);
    
    // Write WAV header
    WavHeader header;
    header.file_size = sizeof(WavHeader) - 8 + data_size;
    header.channels = channels;
    header.sample_rate = sample_rate;
    header.byte_rate = sample_rate * channels * sizeof(int16_t);
    header.block_align = channels * sizeof(int16_t);
    header.bits_per_sample = 16;
    header.data_size = data_size;
    
    file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    
    // Write PCM data
    for (const auto& pcm : pcm_data) {
        file.write(reinterpret_cast<const char*>(pcm.samples.data()), 
                  pcm.samples.size() * sizeof(int16_t));
    }
    
    file.close();
    
    std::cout << "Audio restored to: " << output_file << std::endl;
    std::cout << "Duration: " << last_stats_.duration_ms << " ms" << std::endl;
    std::cout << "Sample rate: " << sample_rate << " Hz" << std::endl;
    std::cout << "Channels: " << channels << std::endl;
    
    return true;
}

bool MediaRestorer::writePcmFile(const std::vector<PcmData>& pcm_data,
                                const std::string& output_file) {
    if (pcm_data.empty()) {
        return false;
    }
    
    std::ofstream file(output_file, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    // Write raw PCM data
    for (const auto& pcm : pcm_data) {
        file.write(reinterpret_cast<const char*>(pcm.samples.data()), 
                  pcm.samples.size() * sizeof(int16_t));
    }
    
    file.close();
    return true;
}

std::string MediaRestorer::generateOutputFileName(const MediaSession* session, 
                                                 const std::string& suffix) {
    if (!session) {
        return "";
    }
    
    std::string filename = session->getCallId();
    // Replace invalid filename characters
    std::replace_if(filename.begin(), filename.end(), 
                   [](char c) { return c == '/' || c == '\\' || c == ':' || c == '*' || 
                               c == '?' || c == '"' || c == '<' || c == '>' || c == '|'; }, 
                   '_');
    
    return config_.output_directory + "/" + filename + suffix;
}

std::string MediaRestorer::generateOutputFileName(const rtp::RtpStream* stream) {
    if (!stream) {
        return "";
    }
    
    std::string filename = "stream_" + std::to_string(stream->getSsrc());
    std::string extension = stream->isAudio() ? ".wav" : ".mp4";
    
    return config_.output_directory + "/" + filename + extension;
}

bool MediaRestorer::createOutputDirectory() {
    try {
        std::filesystem::create_directories(config_.output_directory);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create output directory: " << e.what() << std::endl;
        return false;
    }
}

void MediaRestorer::fillGaps(std::vector<PcmData>& pcm_data, uint32_t sample_rate) {
    if (pcm_data.size() < 2) {
        return;
    }
    
    for (size_t i = 1; i < pcm_data.size(); ++i) {
        uint32_t prev_end_time = pcm_data[i-1].timestamp + 
                                (pcm_data[i-1].samples.size() * 1000 / sample_rate);
        uint32_t curr_start_time = pcm_data[i].timestamp;
        
        if (curr_start_time > prev_end_time) {
            uint32_t gap_ms = curr_start_time - prev_end_time;
            if (gap_ms <= config_.max_gap_ms) {
                // Fill gap with silence
                PcmData silence = MediaUtils::generateSilence(gap_ms, sample_rate, 
                                                            pcm_data[i-1].channels);
                silence.timestamp = prev_end_time;
                pcm_data.insert(pcm_data.begin() + i, silence);
                i++;  // Skip the inserted silence
            }
        }
    }
}

void MediaRestorer::mergePcmData(const std::vector<std::vector<PcmData>>& multi_stream_data,
                                std::vector<PcmData>& merged_data) {
    // Simple implementation: just concatenate all streams
    // A more sophisticated implementation would mix them properly
    for (const auto& stream_data : multi_stream_data) {
        merged_data.insert(merged_data.end(), stream_data.begin(), stream_data.end());
    }
    
    // Sort by timestamp
    std::sort(merged_data.begin(), merged_data.end(),
              [](const PcmData& a, const PcmData& b) {
                  return a.timestamp < b.timestamp;
              });
}

// MediaUtils implementation
uint32_t MediaUtils::calculateDurationMs(const std::vector<PcmData>& pcm_data) {
    if (pcm_data.empty()) {
        return 0;
    }

    uint32_t total_samples = 0;
    uint32_t sample_rate = pcm_data[0].sample_rate;

    for (const auto& pcm : pcm_data) {
        total_samples += pcm.samples.size() / pcm.channels;
    }

    return (total_samples * 1000) / sample_rate;
}

bool MediaUtils::resamplePcm(const PcmData& input, PcmData& output,
                            uint32_t target_sample_rate) {
    if (input.sample_rate == target_sample_rate) {
        output = input;
        return true;
    }

    // Simple linear interpolation resampling
    double ratio = static_cast<double>(target_sample_rate) / input.sample_rate;
    uint32_t output_samples = static_cast<uint32_t>(input.samples.size() * ratio);

    output.samples.clear();
    output.samples.reserve(output_samples);
    output.sample_rate = target_sample_rate;
    output.channels = input.channels;
    output.timestamp = input.timestamp;

    for (uint32_t i = 0; i < output_samples; ++i) {
        double src_index = i / ratio;
        uint32_t src_i = static_cast<uint32_t>(src_index);

        if (src_i < input.samples.size()) {
            output.samples.push_back(input.samples[src_i]);
        } else {
            output.samples.push_back(0);
        }
    }

    return true;
}

bool MediaUtils::mixPcmStreams(const std::vector<PcmData>& inputs, PcmData& output) {
    if (inputs.empty()) {
        return false;
    }

    // Find the longest stream
    size_t max_samples = 0;
    for (const auto& input : inputs) {
        max_samples = std::max(max_samples, input.samples.size());
    }

    output.samples.clear();
    output.samples.resize(max_samples, 0);
    output.sample_rate = inputs[0].sample_rate;
    output.channels = inputs[0].channels;
    output.timestamp = inputs[0].timestamp;

    // Mix all streams
    for (const auto& input : inputs) {
        for (size_t i = 0; i < input.samples.size() && i < max_samples; ++i) {
            int32_t mixed = static_cast<int32_t>(output.samples[i]) + input.samples[i];
            // Clamp to 16-bit range
            mixed = std::max(-32768, std::min(32767, mixed));
            output.samples[i] = static_cast<int16_t>(mixed);
        }
    }

    return true;
}

PcmData MediaUtils::generateSilence(uint32_t duration_ms, uint32_t sample_rate,
                                   uint16_t channels) {
    PcmData silence;
    silence.sample_rate = sample_rate;
    silence.channels = channels;
    silence.timestamp = 0;

    uint32_t total_samples = (duration_ms * sample_rate * channels) / 1000;
    silence.samples.resize(total_samples, 0);

    return silence;
}

bool MediaUtils::validateWavFile(const std::string& file_path) {
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    WavHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(header));

    if (file.gcount() != sizeof(header)) {
        return false;
    }

    // Check RIFF and WAVE signatures
    if (std::memcmp(header.riff, "RIFF", 4) != 0 ||
        std::memcmp(header.wave, "WAVE", 4) != 0 ||
        std::memcmp(header.fmt, "fmt ", 4) != 0 ||
        std::memcmp(header.data, "data", 4) != 0) {
        return false;
    }

    return true;
}

} // namespace media
