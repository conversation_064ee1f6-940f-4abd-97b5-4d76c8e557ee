#include "session_manager.h"
#include <algorithm>
#include <chrono>

namespace media {

SessionManager::SessionManager() : next_session_id_(1) {
}

SessionManager::~SessionManager() {
    for (auto& pair : sessions_) {
        delete pair.second;
    }
}

uint32_t SessionManager::createSession(const std::string& call_id, 
                                     const std::string& protocol) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    uint32_t session_id = next_session_id_++;
    MediaSession* session = new MediaSession(session_id, call_id, protocol);
    
    sessions_[session_id] = session;
    call_id_to_session_[call_id] = session_id;
    
    return session_id;
}

MediaSession* SessionManager::getSession(uint32_t session_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = sessions_.find(session_id);
    return (it != sessions_.end()) ? it->second : nullptr;
}

MediaSession* SessionManager::getSessionByCallId(const std::string& call_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = call_id_to_session_.find(call_id);
    if (it != call_id_to_session_.end()) {
        return sessions_[it->second];
    }
    return nullptr;
}

bool SessionManager::addRtpStream(uint32_t session_id, uint32_t ssrc, 
                                const RtpStreamInfo& stream_info) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = sessions_.find(session_id);
    if (it != sessions_.end()) {
        return it->second->addRtpStream(ssrc, stream_info);
    }
    return false;
}

bool SessionManager::removeSession(uint32_t session_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = sessions_.find(session_id);
    if (it != sessions_.end()) {
        MediaSession* session = it->second;
        call_id_to_session_.erase(session->getCallId());
        sessions_.erase(it);
        delete session;
        return true;
    }
    return false;
}

std::vector<MediaSession*> SessionManager::getAllSessions() {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<MediaSession*> result;
    for (auto& pair : sessions_) {
        result.push_back(pair.second);
    }
    return result;
}

// MediaSession implementation
MediaSession::MediaSession(uint32_t id, const std::string& call_id, 
                         const std::string& protocol)
    : id_(id), call_id_(call_id), protocol_(protocol), 
      state_(SessionState::SETUP), 
      creation_time_(std::chrono::system_clock::now()) {
}

MediaSession::~MediaSession() {
}

bool MediaSession::addRtpStream(uint32_t ssrc, const RtpStreamInfo& stream_info) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Check if stream already exists
    auto it = rtp_streams_.find(ssrc);
    if (it != rtp_streams_.end()) {
        return false;  // Stream already exists
    }
    
    rtp_streams_[ssrc] = stream_info;
    return true;
}

bool MediaSession::removeRtpStream(uint32_t ssrc) {
    std::lock_guard<std::mutex> lock(mutex_);
    return rtp_streams_.erase(ssrc) > 0;
}

RtpStreamInfo* MediaSession::getRtpStream(uint32_t ssrc) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = rtp_streams_.find(ssrc);
    return (it != rtp_streams_.end()) ? &it->second : nullptr;
}

std::vector<RtpStreamInfo> MediaSession::getAllRtpStreams() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<RtpStreamInfo> result;
    for (const auto& pair : rtp_streams_) {
        result.push_back(pair.second);
    }
    return result;
}

std::vector<RtpStreamInfo> MediaSession::getAudioStreams() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<RtpStreamInfo> result;
    for (const auto& pair : rtp_streams_) {
        if (pair.second.media_type == MediaType::AUDIO) {
            result.push_back(pair.second);
        }
    }
    return result;
}

std::vector<RtpStreamInfo> MediaSession::getVideoStreams() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<RtpStreamInfo> result;
    for (const auto& pair : rtp_streams_) {
        if (pair.second.media_type == MediaType::VIDEO) {
            result.push_back(pair.second);
        }
    }
    return result;
}

void MediaSession::setState(SessionState state) {
    std::lock_guard<std::mutex> lock(mutex_);
    state_ = state;
}

SessionState MediaSession::getState() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return state_;
}

void MediaSession::addSdpInfo(const std::string& sdp) {
    std::lock_guard<std::mutex> lock(mutex_);
    sdp_info_ = sdp;
}

std::string MediaSession::getSdpInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return sdp_info_;
}

SessionStats MediaSession::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    SessionStats stats;
    stats.session_id = id_;
    stats.call_id = call_id_;
    stats.protocol = protocol_;
    stats.state = state_;
    stats.creation_time = creation_time_;
    stats.stream_count = rtp_streams_.size();
    
    // Count audio and video streams
    for (const auto& pair : rtp_streams_) {
        if (pair.second.media_type == MediaType::AUDIO) {
            stats.audio_stream_count++;
        } else if (pair.second.media_type == MediaType::VIDEO) {
            stats.video_stream_count++;
        }
    }
    
    return stats;
}

bool MediaSession::hasAudio() const {
    std::lock_guard<std::mutex> lock(mutex_);
    for (const auto& pair : rtp_streams_) {
        if (pair.second.media_type == MediaType::AUDIO) {
            return true;
        }
    }
    return false;
}

bool MediaSession::hasVideo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    for (const auto& pair : rtp_streams_) {
        if (pair.second.media_type == MediaType::VIDEO) {
            return true;
        }
    }
    return false;
}

} // namespace media
