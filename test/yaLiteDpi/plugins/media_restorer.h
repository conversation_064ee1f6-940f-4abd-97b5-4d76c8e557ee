#ifndef MEDIA_RESTORER_H
#define MEDIA_RESTORER_H

#include "rtp_engine.h"
#include "session_manager.h"
#include "audio_decoder.h"
#include <string>
#include <memory>
#include <vector>
#include <fstream>
#include <mutex>
#include <cstring>

namespace media {

// Output format types
enum class OutputFormat {
    WAV,    // Audio only
    MP4,    // Video with audio
    PCM     // Raw PCM audio
};

// Restoration configuration
struct RestorationConfig {
    std::string output_directory;
    OutputFormat format;
    bool merge_streams;          // Merge multiple streams into single file
    uint32_t max_gap_ms;         // Maximum gap to fill with silence (ms)
    bool enable_jitter_buffer;   // Enable jitter buffer for reordering
    uint32_t jitter_buffer_size; // Jitter buffer size in packets
    
    RestorationConfig() : output_directory("./output"), format(OutputFormat::WAV),
                         merge_streams(true), max_gap_ms(1000), 
                         enable_jitter_buffer(true), jitter_buffer_size(50) {}
};

// WAV file header structure
struct WavHeader {
    char riff[4];           // "RIFF"
    uint32_t file_size;     // File size - 8
    char wave[4];           // "WAVE"
    char fmt[4];            // "fmt "
    uint32_t fmt_size;      // Format chunk size (16)
    uint16_t audio_format;  // Audio format (1 = PCM)
    uint16_t channels;      // Number of channels
    uint32_t sample_rate;   // Sample rate
    uint32_t byte_rate;     // Byte rate
    uint16_t block_align;   // Block align
    uint16_t bits_per_sample; // Bits per sample
    char data[4];           // "data"
    uint32_t data_size;     // Data size
    
    WavHeader() {
        std::memcpy(riff, "RIFF", 4);
        std::memcpy(wave, "WAVE", 4);
        std::memcpy(fmt, "fmt ", 4);
        std::memcpy(data, "data", 4);
        fmt_size = 16;
        audio_format = 1;  // PCM
    }
};

// Jitter buffer for packet reordering
class JitterBuffer {
public:
    JitterBuffer(uint32_t max_size);
    ~JitterBuffer();
    
    // Add packet to buffer
    void addPacket(const rtp::RtpPacket& packet);
    
    // Get next packet in sequence
    bool getNextPacket(rtp::RtpPacket& packet);
    
    // Check if buffer has packets ready
    bool hasPackets() const;
    
    // Clear buffer
    void clear();
    
private:
    struct BufferEntry {
        rtp::RtpPacket packet;
        bool valid;
        
        BufferEntry() : valid(false) {}
    };
    
    std::vector<BufferEntry> buffer_;
    uint32_t max_size_;
    uint16_t base_seq_;
    bool seq_initialized_;
    mutable std::mutex mutex_;
    
    uint32_t getBufferIndex(uint16_t seq) const;
};

// Media restorer - converts RTP streams to audio/video files
class MediaRestorer {
public:
    MediaRestorer(const RestorationConfig& config);
    ~MediaRestorer();
    
    // Restore single RTP stream to file
    bool restoreStream(rtp::RtpStream* stream, const std::string& output_file);
    
    // Restore media session (multiple streams) to file(s)
    bool restoreSession(MediaSession* session);
    
    // Restore audio streams to WAV file
    bool restoreAudioToWav(const std::vector<rtp::RtpStream*>& streams, 
                          const std::string& output_file);
    
    // Get restoration statistics
    struct RestorationStats {
        uint32_t total_packets;
        uint32_t decoded_packets;
        uint32_t lost_packets;
        uint32_t duration_ms;
        std::string output_file;
    };
    
    RestorationStats getLastStats() const { return last_stats_; }
    
private:
    RestorationConfig config_;
    RestorationStats last_stats_;
    std::unique_ptr<JitterBuffer> jitter_buffer_;
    
    // Audio restoration helpers
    bool restoreAudioStream(rtp::RtpStream* stream, const std::string& output_file);
    bool writeWavFile(const std::vector<PcmData>& pcm_data, 
                     const std::string& output_file);
    bool writePcmFile(const std::vector<PcmData>& pcm_data,
                     const std::string& output_file);
    
    // Utility functions
    std::string generateOutputFileName(const MediaSession* session, 
                                     const std::string& suffix = "");
    std::string generateOutputFileName(const rtp::RtpStream* stream);
    bool createOutputDirectory();
    
    // PCM data processing
    std::vector<PcmData> decodeRtpStream(rtp::RtpStream* stream);
    void fillGaps(std::vector<PcmData>& pcm_data, uint32_t sample_rate);
    void mergePcmData(const std::vector<std::vector<PcmData>>& multi_stream_data,
                     std::vector<PcmData>& merged_data);
};

// Utility functions
class MediaUtils {
public:
    // Calculate duration from PCM data
    static uint32_t calculateDurationMs(const std::vector<PcmData>& pcm_data);
    
    // Resample PCM data to target sample rate
    static bool resamplePcm(const PcmData& input, PcmData& output, 
                           uint32_t target_sample_rate);
    
    // Mix multiple PCM streams
    static bool mixPcmStreams(const std::vector<PcmData>& inputs, 
                             PcmData& output);
    
    // Generate silence PCM data
    static PcmData generateSilence(uint32_t duration_ms, uint32_t sample_rate, 
                                  uint16_t channels);
    
    // Validate WAV file
    static bool validateWavFile(const std::string& file_path);
};

} // namespace media

#endif // MEDIA_RESTORER_H
