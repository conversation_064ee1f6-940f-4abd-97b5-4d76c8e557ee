#ifndef RTP_ENGINE_H
#define RTP_ENGINE_H

#include <cstdint>
#include <vector>
#include <map>
#include <string>
#include <mutex>

namespace rtp {

// RTP Packet structure
struct RtpPacket {
    // RTP Header fields
    uint8_t  version;
    bool     padding;
    bool     extension;
    uint8_t  cc;           // CSRC count
    bool     marker;
    uint8_t  payload_type;
    uint16_t sequence;
    uint32_t timestamp;
    uint32_t ssrc;
    
    // Extension header (if present)
    uint16_t ext_profile;
    uint16_t ext_length;
    uint8_t* ext_data;
    
    // CSRC list (if cc > 0)
    uint32_t* csrc_list;
    
    // Payload
    uint8_t* payload;
    uint32_t payload_length;
    
    // Metadata
    uint64_t arrival_time;  // Microseconds since epoch
    
    RtpPacket() : version(2), padding(false), extension(false), cc(0), 
                  marker(false), payload_type(0), sequence(0), timestamp(0), 
                  ssrc(0), ext_profile(0), ext_length(0), ext_data(nullptr),
                  csrc_list(nullptr), payload(nullptr), payload_length(0), 
                  arrival_time(0) {}
};

// RTP Stream statistics
struct RtpStreamStats {
    uint32_t stream_id;
    uint32_t ssrc;
    uint8_t  payload_type;
    uint32_t packet_count;
    uint64_t byte_count;
    uint32_t lost_packets;
    uint32_t first_timestamp;
    uint32_t last_timestamp;
    uint16_t first_sequence;
    uint16_t last_sequence;
};

// Forward declaration
class RtpStream;

// RTP Engine - manages multiple RTP streams
class RtpEngine {
public:
    RtpEngine();
    ~RtpEngine();
    
    // Process incoming RTP packet
    uint32_t processRtpPacket(const RtpPacket& packet);
    
    // Get stream by SSRC
    RtpStream* getStream(uint32_t ssrc);
    
    // Get all streams
    std::vector<RtpStream*> getAllStreams();
    
    // Utility functions
    static std::string getPayloadTypeName(uint8_t pt);
    
private:
    std::map<uint32_t, RtpStream*> streams_;  // SSRC -> Stream mapping
    uint32_t next_stream_id_;
    std::mutex mutex_;
    
    static const std::map<uint8_t, std::string> payload_type_names;
    
    RtpStream* findOrCreateStream(uint32_t ssrc, uint8_t payload_type);
};

// RTP Stream - represents a single RTP stream (identified by SSRC)
class RtpStream {
public:
    RtpStream(uint32_t id, uint32_t ssrc, uint8_t payload_type);
    ~RtpStream();
    
    // Add packet to stream
    void addPacket(const RtpPacket& packet);
    
    // Get all packets in sequence order
    std::vector<RtpPacket> getPackets() const;
    
    // Get stream statistics
    RtpStreamStats getStats() const;
    
    // Getters
    uint32_t getId() const { return id_; }
    uint32_t getSsrc() const { return ssrc_; }
    uint8_t getPayloadType() const { return payload_type_; }
    
    // Media type detection
    bool isAudio() const;
    bool isVideo() const;
    
private:
    uint32_t id_;
    uint32_t ssrc_;
    uint8_t payload_type_;
    
    std::vector<RtpPacket> packets_;
    uint32_t packet_count_;
    uint64_t byte_count_;
    
    // Sequence tracking for loss detection
    uint16_t last_seq_;
    bool seq_initialized_;
    uint32_t lost_packets_;
    
    mutable std::mutex mutex_;
};

} // namespace rtp

#endif // RTP_ENGINE_H
