#!/bin/bash
# Master test runner for RTP restoration test suite
# Generated automatically by RtpTestGenerator

TOTAL_TESTS=10
PASSED_TESTS=0
FAILED_TESTS=0

echo "Running RTP Restoration Test Suite"
echo "Total test cases: $TOTAL_TESTS"
echo "==========================================="

echo "Running test: g711a"
cd g711a
if ./run_test.sh; then
    echo "✓ g711a PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g711a FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: g711u"
cd g711u
if ./run_test.sh; then
    echo "✓ g711u PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g711u FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: g722"
cd g722
if ./run_test.sh; then
    echo "✓ g722 PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g722 FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: g723"
cd g723
if ./run_test.sh; then
    echo "✓ g723 PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g723 FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: g728"
cd g728
if ./run_test.sh; then
    echo "✓ g728 PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g728 FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: g729"
cd g729
if ./run_test.sh; then
    echo "✓ g729 PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ g729 FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: opus"
cd opus
if ./run_test.sh; then
    echo "✓ opus PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ opus FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: amr"
cd amr
if ./run_test.sh; then
    echo "✓ amr PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ amr FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: h263p"
cd h263p
if ./run_test.sh; then
    echo "✓ h263p PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ h263p FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "Running test: h264"
cd h264
if ./run_test.sh; then
    echo "✓ h264 PASSED"
    ((PASSED_TESTS++))
else
    echo "✗ h264 FAILED"
    ((FAILED_TESTS++))
fi
cd ..
echo

echo "==========================================="
echo "Test Results Summary:"
echo "Total: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 All tests passed!"
    exit 0
else
    echo "❌ Some tests failed"
    exit 1
fi
