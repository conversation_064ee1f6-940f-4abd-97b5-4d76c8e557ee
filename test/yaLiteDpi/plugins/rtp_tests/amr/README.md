# Test Case: AMR

## Overview
This test case validates RTP stream restoration for AMR codec.

## Input
- **PCAP File**: `amr.pcap`
- **Codec**: AMR
- **Payload Type**: 97

## Expected Output
- **Audio File**: WAV format with restored audio
- **Sample Rate**: Depends on codec (typically 8kHz for telephony codecs)
- **Channels**: Mono (1 channel)
- **Bit Depth**: 16-bit PCM

## Test Criteria
1. **File Generation**: Output WAV file should be created
2. **File Validity**: WAV file should have valid header and structure
3. **Audio Content**: Restored audio should be audible and clear
4. **Duration**: Duration should match expected call length
5. **No Errors**: No parsing or decoding errors should occur

## Running the Test
```bash
./run_test.sh
```

## Manual Verification
After running the test, manually verify the output:
1. Check that WAV files are generated in `output/` directory
2. Play the WAV files to verify audio quality
3. Check file properties (duration, sample rate, etc.)

## Codec-Specific Notes
- Refer to codec specification for expected characteristics
