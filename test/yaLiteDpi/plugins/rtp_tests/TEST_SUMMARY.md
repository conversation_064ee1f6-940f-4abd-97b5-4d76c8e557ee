# RTP Restoration Test Suite

This directory contains test cases for validating RTP stream restoration functionality.

## Test Cases

| Test Name | Codec | Description | PCAP File |
|-----------|-------|-------------|----------|
| g711a | G.711 A-law | G.711 A-law | g711a.pcap |
| g711u | Video | G.711 μ-law | g711u.pcap |
| g722 | G.722 | G.722 | g722.pcap |
| g723 | G.723.1 | G.723.1 | g723.1.pcap |
| g728 | G.728 | G.728 | g728.pcap |
| g729 | G.729 | G.729 | g729.pcap |
| opus | Opus | Opus | opus.pcap |
| amr | AMR | AMR | amr.pcap |
| h263p | Video | H.263+ | h263p.pcap |
| h264 | Video | H.264 | h264.pcap |

## Running All Tests

To run all test cases:
```bash
./run_all_tests.sh
```

To run individual tests:
```bash
cd <test_name>
./run_test.sh
```

## Test Results

Test results will be generated in each test case's `output/` directory.
Check the individual README.md files in each test directory for specific validation criteria.

## Supported Codecs

Currently supported audio codecs:
- G.711 A-law (PCMA)
- G.711 μ-law (PCMU)
- G.722 (wideband)
- L16 (uncompressed linear PCM)

Codecs under development:
- G.723.1
- G.728
- G.729
- Opus
- AMR

Video codecs (future):
- H.263+
- H.264
