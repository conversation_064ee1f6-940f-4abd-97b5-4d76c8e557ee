#!/bin/bash
# Test case for H.264
# Generated automatically by RtpTestGenerator

TEST_NAME="h264"
PCAP_FILE="h264.pcap"
OUTPUT_DIR="./output"
EXPECTED_DIR="./expected"

echo "Running test case: $TEST_NAME"
echo "Input PCAP: $PCAP_FILE"

# Create output directory
mkdir -p $OUTPUT_DIR

# Run yaLiteDpi to process the PCAP file
cd ../../../../..
./bin/yaLiteDpi -f "test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME/$PCAP_FILE" \
    -o "test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME/$OUTPUT_DIR" \
    -p rtp,rtcp,sip

cd "test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME"

# Check if output files were generated
if [ -f "$OUTPUT_DIR/*.wav" ] || [ -f "$OUTPUT_DIR/*.mp4" ]; then
    echo "✓ Output files generated successfully"
    ls -la $OUTPUT_DIR/
else
    echo "✗ No output files generated"
    exit 1
fi

# Validate output files
for wav_file in $OUTPUT_DIR/*.wav; do
    if [ -f "$wav_file" ]; then
        echo "Validating WAV file: $wav_file"
        # Add WAV validation logic here
        file "$wav_file"
    fi
done

echo "Test case $TEST_NAME completed"
