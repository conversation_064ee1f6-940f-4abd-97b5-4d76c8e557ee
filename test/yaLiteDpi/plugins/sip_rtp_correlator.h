#ifndef SIP_RTP_CORRELATOR_H
#define SIP_RTP_CORRELATOR_H

#include "rtp_engine.h"
#include "session_manager.h"
#include "media_restorer.h"
#include <string>
#include <map>
#include <vector>
#include <memory>
#include <regex>

namespace media {

// SDP media description
struct SdpMedia {
    std::string media_type;     // "audio" or "video"
    uint16_t port;              // RTP port
    std::string protocol;       // "RTP/AVP"
    std::vector<uint8_t> payload_types; // Payload type numbers
    std::map<uint8_t, std::string> codecs; // PT -> codec name mapping
    uint32_t sample_rate;       // Sample rate (for audio)
    uint16_t channels;          // Number of channels
    
    SdpMedia() : port(0), sample_rate(8000), channels(1) {}
};

// SIP call information extracted from signaling
struct SipCallInfo {
    std::string call_id;
    std::string from_uri;
    std::string to_uri;
    std::string caller_ip;
    std::string callee_ip;
    std::vector<SdpMedia> media_descriptions;
    std::string method;         // INVITE, BYE, etc.
    uint32_t cseq;
    
    SipCallInfo() : cseq(0) {}
};

// RTP stream correlation information
struct RtpCorrelation {
    uint32_t ssrc;
    std::string call_id;
    uint16_t local_port;
    uint16_t remote_port;
    std::string local_ip;
    std::string remote_ip;
    uint8_t payload_type;
    std::string codec_name;
    MediaType media_type;
    uint32_t session_id;
    
    RtpCorrelation() : ssrc(0), local_port(0), remote_port(0), 
                      payload_type(0), media_type(MediaType::UNKNOWN), 
                      session_id(0) {}
};

// SIP/RTP correlator - associates SIP signaling with RTP media streams
class SipRtpCorrelator {
public:
    SipRtpCorrelator(rtp::RtpEngine& rtp_engine, SessionManager& session_manager);
    ~SipRtpCorrelator();
    
    // Process SIP message
    bool processSipMessage(const std::string& sip_message, 
                          const std::string& src_ip, 
                          const std::string& dst_ip);
    
    // Process RTP packet
    bool processRtpPacket(const rtp::RtpPacket& packet,
                         const std::string& src_ip,
                         const std::string& dst_ip,
                         uint16_t src_port,
                         uint16_t dst_port);
    
    // Get correlation for RTP stream
    RtpCorrelation* getCorrelation(uint32_t ssrc);
    
    // Get all correlations
    std::vector<RtpCorrelation> getAllCorrelations() const;
    
    // Get call information
    SipCallInfo* getCallInfo(const std::string& call_id);
    
    // Restore all media for a call
    bool restoreCall(const std::string& call_id, const std::string& output_dir);
    
private:
    rtp::RtpEngine& rtp_engine_;
    SessionManager& session_manager_;
    
    std::map<std::string, SipCallInfo> call_info_;      // call_id -> call info
    std::map<uint32_t, RtpCorrelation> correlations_;   // ssrc -> correlation
    std::map<std::string, std::vector<uint32_t>> call_streams_; // call_id -> ssrc list
    
    // SIP parsing helpers
    bool parseSipMessage(const std::string& message, SipCallInfo& call_info);
    std::string extractCallId(const std::string& message);
    std::string extractMethod(const std::string& message);
    std::string extractFromUri(const std::string& message);
    std::string extractToUri(const std::string& message);
    uint32_t extractCSeq(const std::string& message);
    
    // SDP parsing helpers
    bool parseSdp(const std::string& sdp, std::vector<SdpMedia>& media_list);
    bool parseSdpMediaLine(const std::string& line, SdpMedia& media);
    bool parseSdpAttributeLine(const std::string& line, SdpMedia& media);
    
    // Correlation helpers
    bool correlateRtpWithSip(const rtp::RtpPacket& packet,
                            const std::string& src_ip,
                            const std::string& dst_ip,
                            uint16_t src_port,
                            uint16_t dst_port);
    
    MediaType getMediaTypeFromPayloadType(uint8_t pt);
    std::string getCodecNameFromPayloadType(uint8_t pt);
};

// Utility functions for SIP/SDP parsing
class SipSdpParser {
public:
    // Extract header value from SIP message
    static std::string extractHeader(const std::string& message, 
                                   const std::string& header_name);
    
    // Extract SDP from SIP message body
    static std::string extractSdp(const std::string& message);
    
    // Parse SDP connection line (c=)
    static bool parseConnectionLine(const std::string& line, 
                                  std::string& network_type,
                                  std::string& address_type,
                                  std::string& connection_address);
    
    // Parse SDP media line (m=)
    static bool parseMediaLine(const std::string& line,
                             std::string& media_type,
                             uint16_t& port,
                             std::string& protocol,
                             std::vector<uint8_t>& payload_types);
    
    // Parse SDP attribute line (a=)
    static bool parseAttributeLine(const std::string& line,
                                 std::string& attribute,
                                 std::string& value);
    
    // Parse rtpmap attribute (a=rtpmap:)
    static bool parseRtpMap(const std::string& value,
                          uint8_t& payload_type,
                          std::string& codec_name,
                          uint32_t& sample_rate,
                          uint16_t& channels);
};

} // namespace media

#endif // SIP_RTP_CORRELATOR_H
