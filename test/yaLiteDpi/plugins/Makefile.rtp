# Makefile for RTP Media Restorer
# This builds the standalone RTP restoration tools

CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
INCLUDES = -I.

# Source files
RTP_ENGINE_SOURCES = rtp_engine.cpp
SESSION_MANAGER_SOURCES = session_manager.cpp
AUDIO_DECODER_SOURCES = audio_decoder.cpp
MEDIA_RESTORER_SOURCES = media_restorer.cpp
TEST_GENERATOR_SOURCES = rtp_test_generator.cpp
SIP_RTP_CORRELATOR_SOURCES = sip_rtp_correlator.cpp
MAIN_SOURCES = rtp_restorer_main.cpp
DEMO_SOURCES = sip_rtp_demo.cpp

# Object files
RTP_ENGINE_OBJS = $(RTP_ENGINE_SOURCES:.cpp=.o)
SESSION_MANAGER_OBJS = $(SESSION_MANAGER_SOURCES:.cpp=.o)
AUDIO_DECODER_OBJS = $(AUDIO_DECODER_SOURCES:.cpp=.o)
MEDIA_RESTORER_OBJS = $(MEDIA_RESTORER_SOURCES:.cpp=.o)
TEST_GENERATOR_OBJS = $(TEST_GENERATOR_SOURCES:.cpp=.o)
SIP_RTP_CORRELATOR_OBJS = $(SIP_RTP_CORRELATOR_SOURCES:.cpp=.o)
MAIN_OBJS = $(MAIN_SOURCES:.cpp=.o)
DEMO_OBJS = $(DEMO_SOURCES:.cpp=.o)

# All object files
ALL_OBJS = $(RTP_ENGINE_OBJS) $(SESSION_MANAGER_OBJS) $(AUDIO_DECODER_OBJS) $(MEDIA_RESTORER_OBJS) $(SIP_RTP_CORRELATOR_OBJS)

# Executables
RTP_RESTORER = rtp_restorer
TEST_GENERATOR = generate_rtp_tests
SIP_RTP_DEMO = sip_rtp_demo

# Default target
all: $(RTP_RESTORER) $(TEST_GENERATOR) $(SIP_RTP_DEMO)

# RTP Restorer executable
$(RTP_RESTORER): $(ALL_OBJS) $(MAIN_OBJS)
	$(CXX) $(CXXFLAGS) -o $@ $^

# Test Generator executable
$(TEST_GENERATOR): $(AUDIO_DECODER_OBJS) $(TEST_GENERATOR_OBJS) generate_rtp_tests.o
	$(CXX) $(CXXFLAGS) -o $@ $^

# SIP/RTP Demo executable
$(SIP_RTP_DEMO): $(ALL_OBJS) $(DEMO_OBJS)
	$(CXX) $(CXXFLAGS) -o $@ $^

# Object file rules
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Generate test cases
generate-tests: $(TEST_GENERATOR)
	./$(TEST_GENERATOR)

# Run demo
demo: $(RTP_RESTORER)
	./$(RTP_RESTORER) --demo

# Run SIP/RTP correlation demo
sip-demo: $(SIP_RTP_DEMO)
	./$(SIP_RTP_DEMO)

# Clean
clean:
	rm -f *.o $(RTP_RESTORER) $(TEST_GENERATOR)
	rm -rf output/

# Clean tests
clean-tests:
	rm -rf rtp_tests/

# Install (copy to bin directory)
install: $(RTP_RESTORER) $(TEST_GENERATOR)
	mkdir -p ../../../bin
	cp $(RTP_RESTORER) ../../../bin/
	cp $(TEST_GENERATOR) ../../../bin/

# Help
help:
	@echo "Available targets:"
	@echo "  all            - Build all executables"
	@echo "  $(RTP_RESTORER)      - Build RTP restorer"
	@echo "  $(TEST_GENERATOR) - Build test generator"
	@echo "  generate-tests - Generate test cases"
	@echo "  demo           - Run demo with synthetic data"
	@echo "  clean          - Clean object files and executables"
	@echo "  clean-tests    - Clean generated test cases"
	@echo "  install        - Install to bin directory"
	@echo "  help           - Show this help"

# Dependencies
rtp_engine.o: rtp_engine.h
session_manager.o: session_manager.h
audio_decoder.o: audio_decoder.h
media_restorer.o: media_restorer.h rtp_engine.h session_manager.h audio_decoder.h
rtp_test_generator.o: rtp_test_generator.h audio_decoder.h
rtp_restorer_main.o: rtp_engine.h session_manager.h media_restorer.h audio_decoder.h
generate_rtp_tests.o: rtp_test_generator.h

.PHONY: all clean clean-tests generate-tests demo install help
