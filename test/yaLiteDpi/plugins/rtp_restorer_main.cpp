#include "rtp_engine.h"
#include "session_manager.h"
#include "media_restorer.h"
#include "audio_decoder.h"
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <memory>

// Simple RTP packet parser for demonstration
class SimpleRtpParser {
public:
    static bool parseRtpPacket(const uint8_t* data, size_t length, rtp::RtpPacket& packet) {
        if (length < 12) {
            return false;
        }
        
        // Parse RTP header
        uint8_t first_byte = data[0];
        uint8_t second_byte = data[1];
        
        packet.version = (first_byte >> 6) & 0x03;
        packet.padding = (first_byte >> 5) & 0x01;
        packet.extension = (first_byte >> 4) & 0x01;
        packet.cc = first_byte & 0x0F;
        
        packet.marker = (second_byte >> 7) & 0x01;
        packet.payload_type = second_byte & 0x7F;
        
        packet.sequence = (data[2] << 8) | data[3];
        packet.timestamp = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
        packet.ssrc = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];
        
        // Calculate header length
        int header_len = 12 + (packet.cc * 4);
        
        // Handle extension header
        if (packet.extension && length >= header_len + 4) {
            packet.ext_profile = (data[header_len] << 8) | data[header_len + 1];
            packet.ext_length = (data[header_len + 2] << 8) | data[header_len + 3];
            header_len += 4 + (packet.ext_length * 4);
        }
        
        // Extract payload
        if (length > header_len) {
            packet.payload_length = length - header_len;
            packet.payload = new uint8_t[packet.payload_length];
            std::memcpy(packet.payload, data + header_len, packet.payload_length);
        } else {
            packet.payload_length = 0;
            packet.payload = nullptr;
        }
        
        return true;
    }
};

// Demo function to create sample RTP packets for testing
std::vector<rtp::RtpPacket> createSampleRtpPackets() {
    std::vector<rtp::RtpPacket> packets;
    
    // Create some sample G.711 μ-law packets
    for (int i = 0; i < 10; ++i) {
        rtp::RtpPacket packet;
        packet.version = 2;
        packet.padding = false;
        packet.extension = false;
        packet.cc = 0;
        packet.marker = (i == 9);  // Mark last packet
        packet.payload_type = 0;   // G.711 μ-law
        packet.sequence = i;
        packet.timestamp = i * 160; // 20ms @ 8kHz
        packet.ssrc = 0x12345678;
        
        // Create sample μ-law payload (silence)
        packet.payload_length = 160; // 20ms @ 8kHz
        packet.payload = new uint8_t[packet.payload_length];
        std::memset(packet.payload, 0xFF, packet.payload_length); // μ-law silence
        
        packets.push_back(packet);
    }
    
    return packets;
}

int main(int argc, char* argv[]) {
    std::cout << "RTP Media Restorer Demo" << std::endl;
    std::cout << "======================" << std::endl;
    
    // Parse command line arguments
    std::string input_file;
    std::string output_dir = "./output";
    bool demo_mode = false;
    
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <input.pcap> [output_dir]" << std::endl;
        std::cout << "   or: " << argv[0] << " --demo [output_dir]" << std::endl;
        std::cout << std::endl;
        std::cout << "Running in demo mode with synthetic data..." << std::endl;
        demo_mode = true;
    } else {
        input_file = argv[1];
        if (input_file == "--demo") {
            demo_mode = true;
        }
        if (argc > 2) {
            output_dir = argv[2];
        }
    }
    
    try {
        // Initialize components
        rtp::RtpEngine rtp_engine;
        media::SessionManager session_manager;
        
        media::RestorationConfig config;
        config.output_directory = output_dir;
        config.format = media::OutputFormat::WAV;
        config.merge_streams = true;
        config.max_gap_ms = 1000;
        config.enable_jitter_buffer = true;
        
        media::MediaRestorer restorer(config);
        
        std::cout << "Output directory: " << output_dir << std::endl;
        std::cout << std::endl;
        
        if (demo_mode) {
            // Demo mode: create synthetic RTP packets
            std::cout << "Creating synthetic RTP packets..." << std::endl;
            
            // Create a media session
            uint32_t session_id = session_manager.createSession("demo-call-001", "SIP");
            media::MediaSession* session = session_manager.getSession(session_id);
            
            if (!session) {
                std::cerr << "Failed to create media session" << std::endl;
                return 1;
            }
            
            // Create sample RTP packets
            std::vector<rtp::RtpPacket> packets = createSampleRtpPackets();
            std::cout << "Created " << packets.size() << " sample RTP packets" << std::endl;
            
            // Process packets through RTP engine
            for (const auto& packet : packets) {
                uint32_t stream_id = rtp_engine.processRtpPacket(packet);
                std::cout << "Processed packet seq=" << packet.sequence 
                         << " timestamp=" << packet.timestamp 
                         << " stream_id=" << stream_id << std::endl;
            }
            
            // Get the RTP stream
            rtp::RtpStream* stream = rtp_engine.getStream(0x12345678);
            if (stream) {
                std::cout << std::endl;
                std::cout << "Stream statistics:" << std::endl;
                auto stats = stream->getStats();
                std::cout << "  SSRC: 0x" << std::hex << stats.ssrc << std::dec << std::endl;
                std::cout << "  Payload Type: " << static_cast<int>(stats.payload_type) << std::endl;
                std::cout << "  Packet Count: " << stats.packet_count << std::endl;
                std::cout << "  Byte Count: " << stats.byte_count << std::endl;
                std::cout << "  Lost Packets: " << stats.lost_packets << std::endl;
                std::cout << "  Codec: " << rtp::RtpEngine::getPayloadTypeName(stats.payload_type) << std::endl;
                
                // Restore audio
                std::string output_file = output_dir + "/demo_call_001.wav";
                std::cout << std::endl;
                std::cout << "Restoring audio to: " << output_file << std::endl;
                
                if (restorer.restoreStream(stream, output_file)) {
                    auto restore_stats = restorer.getLastStats();
                    std::cout << "✅ Audio restoration successful!" << std::endl;
                    std::cout << "  Total packets: " << restore_stats.total_packets << std::endl;
                    std::cout << "  Decoded packets: " << restore_stats.decoded_packets << std::endl;
                    std::cout << "  Duration: " << restore_stats.duration_ms << " ms" << std::endl;
                    std::cout << "  Output file: " << restore_stats.output_file << std::endl;
                } else {
                    std::cerr << "❌ Audio restoration failed" << std::endl;
                    return 1;
                }
            } else {
                std::cerr << "No RTP stream found" << std::endl;
                return 1;
            }
            
        } else {
            // PCAP mode: parse real PCAP file
            std::cout << "PCAP parsing not implemented yet" << std::endl;
            std::cout << "Input file: " << input_file << std::endl;
            std::cout << "This would parse the PCAP file and extract RTP packets" << std::endl;
            return 1;
        }
        
        std::cout << std::endl;
        std::cout << "🎉 RTP restoration completed successfully!" << std::endl;
        
        // Cleanup
        for (auto& packet : createSampleRtpPackets()) {
            delete[] packet.payload;
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
