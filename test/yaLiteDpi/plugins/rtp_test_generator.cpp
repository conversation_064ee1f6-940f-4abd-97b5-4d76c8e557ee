#include "rtp_test_generator.h"
#include "media_restorer.h"
#include <iostream>
#include <fstream>
#include <filesystem>

namespace media {

RtpTestGenerator::RtpTestGenerator(const std::string& pcap_dir, 
                                 const std::string& output_dir)
    : pcap_directory_(pcap_dir), output_directory_(output_dir) {
    
    // Create output directory
    std::filesystem::create_directories(output_directory_);
    
    // Initialize test cases for known codecs
    initializeTestCases();
}

RtpTestGenerator::~RtpTestGenerator() {
}

void RtpTestGenerator::initializeTestCases() {
    // Define test cases for each codec based on available pcap files
    test_cases_ = {
        {"g711a", "G.711 A-law", AudioCodec::G711_ALAW, "test/pcaps/media_pcaps/g711a.pcap"},
        {"g711u", "G.711 μ-law", AudioCodec::G711_ULAW, "test/pcaps/media_pcaps/g711u.pcap"},
        {"g722", "G.722", AudioCodec::G722, "test/pcaps/media_pcaps/g722.pcap"},
        {"g723", "G.723.1", AudioCodec::G723, "test/pcaps/media_pcaps/g723.1.pcap"},
        {"g728", "G.728", AudioCodec::G728, "test/pcaps/media_pcaps/g728.pcap"},
        {"g729", "G.729", AudioCodec::G729, "test/pcaps/media_pcaps/g729.pcap"},
        {"opus", "Opus", AudioCodec::OPUS, "test/pcaps/media_pcaps/opus.pcap"},
        {"amr", "AMR", AudioCodec::AMR, "test/pcaps/media_pcaps/amr.pcap"},
        {"h263p", "H.263+", AudioCodec::UNKNOWN, "test/pcaps/media_pcaps/h263p.pcap"},
        {"h264", "H.264", AudioCodec::UNKNOWN, "test/pcaps/media_pcaps/h264.pcap"}
    };
}

bool RtpTestGenerator::generateAllTests() {
    bool all_success = true;
    
    std::cout << "Generating RTP restoration test cases..." << std::endl;
    
    for (const auto& test_case : test_cases_) {
        std::cout << "Processing " << test_case.name << " (" << test_case.description << ")..." << std::endl;
        
        if (!generateTestCase(test_case)) {
            std::cerr << "Failed to generate test case for " << test_case.name << std::endl;
            all_success = false;
        } else {
            std::cout << "✓ Generated test case for " << test_case.name << std::endl;
        }
    }
    
    // Generate test summary
    generateTestSummary();
    
    return all_success;
}

bool RtpTestGenerator::generateTestCase(const TestCase& test_case) {
    // Check if pcap file exists
    if (!std::filesystem::exists(test_case.pcap_file)) {
        std::cerr << "PCAP file not found: " << test_case.pcap_file << std::endl;
        return false;
    }
    
    // Create test case directory
    std::string test_dir = output_directory_ + "/" + test_case.name;
    std::filesystem::create_directories(test_dir);
    
    // Generate test script
    if (!generateTestScript(test_case, test_dir)) {
        return false;
    }
    
    // Generate expected results documentation
    if (!generateExpectedResults(test_case, test_dir)) {
        return false;
    }
    
    // Copy pcap file to test directory
    std::string dest_pcap = test_dir + "/" + test_case.name + ".pcap";
    try {
        std::filesystem::copy_file(test_case.pcap_file, dest_pcap);
    } catch (const std::exception& e) {
        std::cerr << "Failed to copy pcap file: " << e.what() << std::endl;
        return false;
    }
    
    return true;
}

bool RtpTestGenerator::generateTestScript(const TestCase& test_case, 
                                        const std::string& test_dir) {
    std::string script_file = test_dir + "/run_test.sh";
    std::ofstream script(script_file);
    
    if (!script.is_open()) {
        return false;
    }
    
    script << "#!/bin/bash\n";
    script << "# Test case for " << test_case.description << "\n";
    script << "# Generated automatically by RtpTestGenerator\n\n";
    
    script << "TEST_NAME=\"" << test_case.name << "\"\n";
    script << "PCAP_FILE=\"" << test_case.name << ".pcap\"\n";
    script << "OUTPUT_DIR=\"./output\"\n";
    script << "EXPECTED_DIR=\"./expected\"\n\n";
    
    script << "echo \"Running test case: $TEST_NAME\"\n";
    script << "echo \"Input PCAP: $PCAP_FILE\"\n\n";
    
    script << "# Create output directory\n";
    script << "mkdir -p $OUTPUT_DIR\n\n";
    
    script << "# Run yaLiteDpi to process the PCAP file\n";
    script << "cd ../../../../..\n";
    script << "./bin/yaLiteDpi -f \"test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME/$PCAP_FILE\" \\\n";
    script << "    -o \"test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME/$OUTPUT_DIR\" \\\n";
    script << "    -p rtp,rtcp,sip\n\n";
    
    script << "cd \"test/yaLiteDpi/plugins/rtp_tests/$TEST_NAME\"\n\n";
    
    script << "# Check if output files were generated\n";
    script << "if [ -f \"$OUTPUT_DIR/*.wav\" ] || [ -f \"$OUTPUT_DIR/*.mp4\" ]; then\n";
    script << "    echo \"✓ Output files generated successfully\"\n";
    script << "    ls -la $OUTPUT_DIR/\n";
    script << "else\n";
    script << "    echo \"✗ No output files generated\"\n";
    script << "    exit 1\n";
    script << "fi\n\n";
    
    script << "# Validate output files\n";
    script << "for wav_file in $OUTPUT_DIR/*.wav; do\n";
    script << "    if [ -f \"$wav_file\" ]; then\n";
    script << "        echo \"Validating WAV file: $wav_file\"\n";
    script << "        # Add WAV validation logic here\n";
    script << "        file \"$wav_file\"\n";
    script << "    fi\n";
    script << "done\n\n";
    
    script << "echo \"Test case $TEST_NAME completed\"\n";
    
    script.close();
    
    // Make script executable
    std::filesystem::permissions(script_file, 
                               std::filesystem::perms::owner_exec | 
                               std::filesystem::perms::group_exec | 
                               std::filesystem::perms::others_exec,
                               std::filesystem::perm_options::add);
    
    return true;
}

bool RtpTestGenerator::generateExpectedResults(const TestCase& test_case, 
                                             const std::string& test_dir) {
    std::string readme_file = test_dir + "/README.md";
    std::ofstream readme(readme_file);
    
    if (!readme.is_open()) {
        return false;
    }
    
    readme << "# Test Case: " << test_case.description << "\n\n";
    readme << "## Overview\n";
    readme << "This test case validates RTP stream restoration for " << test_case.description << " codec.\n\n";
    
    readme << "## Input\n";
    readme << "- **PCAP File**: `" << test_case.name << ".pcap`\n";
    readme << "- **Codec**: " << test_case.description << "\n";
    if (test_case.codec != AudioCodec::UNKNOWN) {
        readme << "- **Payload Type**: " << static_cast<int>(test_case.codec) << "\n";
    }
    readme << "\n";
    
    readme << "## Expected Output\n";
    readme << "- **Audio File**: WAV format with restored audio\n";
    readme << "- **Sample Rate**: Depends on codec (typically 8kHz for telephony codecs)\n";
    readme << "- **Channels**: Mono (1 channel)\n";
    readme << "- **Bit Depth**: 16-bit PCM\n\n";
    
    readme << "## Test Criteria\n";
    readme << "1. **File Generation**: Output WAV file should be created\n";
    readme << "2. **File Validity**: WAV file should have valid header and structure\n";
    readme << "3. **Audio Content**: Restored audio should be audible and clear\n";
    readme << "4. **Duration**: Duration should match expected call length\n";
    readme << "5. **No Errors**: No parsing or decoding errors should occur\n\n";
    
    readme << "## Running the Test\n";
    readme << "```bash\n";
    readme << "./run_test.sh\n";
    readme << "```\n\n";
    
    readme << "## Manual Verification\n";
    readme << "After running the test, manually verify the output:\n";
    readme << "1. Check that WAV files are generated in `output/` directory\n";
    readme << "2. Play the WAV files to verify audio quality\n";
    readme << "3. Check file properties (duration, sample rate, etc.)\n\n";
    
    readme << "## Codec-Specific Notes\n";
    switch (test_case.codec) {
        case AudioCodec::G711_ALAW:
        case AudioCodec::G711_ULAW:
            readme << "- G.711 is uncompressed, expect high quality audio\n";
            readme << "- Standard telephony sample rate: 8kHz\n";
            break;
        case AudioCodec::G722:
            readme << "- G.722 provides wideband audio (7kHz bandwidth)\n";
            readme << "- Sample rate: 16kHz\n";
            break;
        case AudioCodec::G729:
            readme << "- G.729 is highly compressed, expect some quality loss\n";
            readme << "- Very efficient for bandwidth-limited scenarios\n";
            break;
        default:
            readme << "- Refer to codec specification for expected characteristics\n";
            break;
    }
    
    readme.close();
    return true;
}

bool RtpTestGenerator::generateTestSummary() {
    std::string summary_file = output_directory_ + "/TEST_SUMMARY.md";
    std::ofstream summary(summary_file);
    
    if (!summary.is_open()) {
        return false;
    }
    
    summary << "# RTP Restoration Test Suite\n\n";
    summary << "This directory contains test cases for validating RTP stream restoration functionality.\n\n";
    
    summary << "## Test Cases\n\n";
    summary << "| Test Name | Codec | Description | PCAP File |\n";
    summary << "|-----------|-------|-------------|----------|\n";
    
    for (const auto& test_case : test_cases_) {
        summary << "| " << test_case.name << " | ";
        if (test_case.codec != AudioCodec::UNKNOWN) {
            summary << AudioDecoderFactory::getCodecName(test_case.codec);
        } else {
            summary << "Video";
        }
        summary << " | " << test_case.description << " | ";
        summary << std::filesystem::path(test_case.pcap_file).filename().string() << " |\n";
    }
    
    summary << "\n## Running All Tests\n\n";
    summary << "To run all test cases:\n";
    summary << "```bash\n";
    summary << "./run_all_tests.sh\n";
    summary << "```\n\n";
    
    summary << "To run individual tests:\n";
    summary << "```bash\n";
    summary << "cd <test_name>\n";
    summary << "./run_test.sh\n";
    summary << "```\n\n";
    
    summary << "## Test Results\n\n";
    summary << "Test results will be generated in each test case's `output/` directory.\n";
    summary << "Check the individual README.md files in each test directory for specific validation criteria.\n\n";
    
    summary << "## Supported Codecs\n\n";
    summary << "Currently supported audio codecs:\n";
    summary << "- G.711 A-law (PCMA)\n";
    summary << "- G.711 μ-law (PCMU)\n";
    summary << "- G.722 (wideband)\n";
    summary << "- L16 (uncompressed linear PCM)\n\n";
    
    summary << "Codecs under development:\n";
    summary << "- G.723.1\n";
    summary << "- G.728\n";
    summary << "- G.729\n";
    summary << "- Opus\n";
    summary << "- AMR\n\n";
    
    summary << "Video codecs (future):\n";
    summary << "- H.263+\n";
    summary << "- H.264\n";
    
    summary.close();
    
    // Generate master test runner script
    generateMasterTestScript();
    
    return true;
}

bool RtpTestGenerator::generateMasterTestScript() {
    std::string script_file = output_directory_ + "/run_all_tests.sh";
    std::ofstream script(script_file);
    
    if (!script.is_open()) {
        return false;
    }
    
    script << "#!/bin/bash\n";
    script << "# Master test runner for RTP restoration test suite\n";
    script << "# Generated automatically by RtpTestGenerator\n\n";
    
    script << "TOTAL_TESTS=" << test_cases_.size() << "\n";
    script << "PASSED_TESTS=0\n";
    script << "FAILED_TESTS=0\n\n";
    
    script << "echo \"Running RTP Restoration Test Suite\"\n";
    script << "echo \"Total test cases: $TOTAL_TESTS\"\n";
    script << "echo \"===========================================\"\n\n";
    
    for (const auto& test_case : test_cases_) {
        script << "echo \"Running test: " << test_case.name << "\"\n";
        script << "cd " << test_case.name << "\n";
        script << "if ./run_test.sh; then\n";
        script << "    echo \"✓ " << test_case.name << " PASSED\"\n";
        script << "    ((PASSED_TESTS++))\n";
        script << "else\n";
        script << "    echo \"✗ " << test_case.name << " FAILED\"\n";
        script << "    ((FAILED_TESTS++))\n";
        script << "fi\n";
        script << "cd ..\n";
        script << "echo\n\n";
    }
    
    script << "echo \"===========================================\"\n";
    script << "echo \"Test Results Summary:\"\n";
    script << "echo \"Total: $TOTAL_TESTS\"\n";
    script << "echo \"Passed: $PASSED_TESTS\"\n";
    script << "echo \"Failed: $FAILED_TESTS\"\n\n";
    
    script << "if [ $FAILED_TESTS -eq 0 ]; then\n";
    script << "    echo \"🎉 All tests passed!\"\n";
    script << "    exit 0\n";
    script << "else\n";
    script << "    echo \"❌ Some tests failed\"\n";
    script << "    exit 1\n";
    script << "fi\n";
    
    script.close();
    
    // Make script executable
    std::filesystem::permissions(script_file, 
                               std::filesystem::perms::owner_exec | 
                               std::filesystem::perms::group_exec | 
                               std::filesystem::perms::others_exec,
                               std::filesystem::perm_options::add);
    
    return true;
}

} // namespace media
