#include "sip_rtp_correlator.h"
#include <iostream>
#include <string>

// Sample SIP INVITE message with SDP
const std::string SAMPLE_SIP_INVITE = 
"INVITE sip:<EMAIL> SIP/2.0\r\n"
"Via: SIP/2.0/UDP *************:5060;branch=z9hG4bK776asdhds\r\n"
"Max-Forwards: 70\r\n"
"To: Bob <sip:<EMAIL>>\r\n"
"From: Alice <sip:<EMAIL>>;tag=1928301774\r\n"
"Call-ID: a84b4c76e66710@*************\r\n"
"CSeq: 314159 INVITE\r\n"
"Contact: <sip:alice@*************>\r\n"
"Content-Type: application/sdp\r\n"
"Content-Length: 142\r\n"
"\r\n"
"v=0\r\n"
"o=alice 53655765 2353687637 IN IP4 *************\r\n"
"s=-\r\n"
"c=IN IP4 *************\r\n"
"t=0 0\r\n"
"m=audio 49170 RTP/AVP 0\r\n"
"a=rtpmap:0 PCMU/8000\r\n";

// Sample SIP 200 OK response with SDP
const std::string SAMPLE_SIP_200OK = 
"SIP/2.0 200 OK\r\n"
"Via: SIP/2.0/UDP *************:5060;branch=z9hG4bK776asdhds\r\n"
"To: Bob <sip:<EMAIL>>;tag=a6c85cf\r\n"
"From: Alice <sip:<EMAIL>>;tag=1928301774\r\n"
"Call-ID: a84b4c76e66710@*************\r\n"
"CSeq: 314159 INVITE\r\n"
"Contact: <sip:bob@*************>\r\n"
"Content-Type: application/sdp\r\n"
"Content-Length: 131\r\n"
"\r\n"
"v=0\r\n"
"o=bob 29738523 2893756231 IN IP4 *************\r\n"
"s=-\r\n"
"c=IN IP4 *************\r\n"
"t=0 0\r\n"
"m=audio 38060 RTP/AVP 0\r\n"
"a=rtpmap:0 PCMU/8000\r\n";

// Create sample RTP packets for the call
std::vector<rtp::RtpPacket> createCallRtpPackets() {
    std::vector<rtp::RtpPacket> packets;
    
    // Alice to Bob (SSRC: 0x11111111, port 49170 -> 38060)
    for (int i = 0; i < 5; ++i) {
        rtp::RtpPacket packet;
        packet.version = 2;
        packet.padding = false;
        packet.extension = false;
        packet.cc = 0;
        packet.marker = false;
        packet.payload_type = 0;   // G.711 μ-law
        packet.sequence = i;
        packet.timestamp = i * 160; // 20ms @ 8kHz
        packet.ssrc = 0x11111111;
        
        // Create sample μ-law payload
        packet.payload_length = 160; // 20ms @ 8kHz
        packet.payload = new uint8_t[packet.payload_length];
        std::memset(packet.payload, 0xFF, packet.payload_length); // μ-law silence
        
        packets.push_back(packet);
    }
    
    // Bob to Alice (SSRC: 0x22222222, port 38060 -> 49170)
    for (int i = 0; i < 5; ++i) {
        rtp::RtpPacket packet;
        packet.version = 2;
        packet.padding = false;
        packet.extension = false;
        packet.cc = 0;
        packet.marker = false;
        packet.payload_type = 0;   // G.711 μ-law
        packet.sequence = i + 100;
        packet.timestamp = (i + 100) * 160; // 20ms @ 8kHz
        packet.ssrc = 0x22222222;
        
        // Create sample μ-law payload
        packet.payload_length = 160; // 20ms @ 8kHz
        packet.payload = new uint8_t[packet.payload_length];
        std::memset(packet.payload, 0xF0, packet.payload_length); // Different pattern
        
        packets.push_back(packet);
    }
    
    return packets;
}

int main() {
    std::cout << "SIP/RTP Correlation Demo" << std::endl;
    std::cout << "========================" << std::endl;
    std::cout << std::endl;
    
    try {
        // Initialize components
        rtp::RtpEngine rtp_engine;
        media::SessionManager session_manager;
        media::SipRtpCorrelator correlator(rtp_engine, session_manager);
        
        std::cout << "1. Processing SIP INVITE message..." << std::endl;
        
        // Process SIP INVITE
        if (correlator.processSipMessage(SAMPLE_SIP_INVITE, "*************", "*************")) {
            std::cout << "✓ SIP INVITE processed successfully" << std::endl;
        } else {
            std::cerr << "✗ Failed to process SIP INVITE" << std::endl;
            return 1;
        }
        
        std::cout << std::endl;
        std::cout << "2. Processing SIP 200 OK response..." << std::endl;
        
        // Process SIP 200 OK
        if (correlator.processSipMessage(SAMPLE_SIP_200OK, "*************", "*************")) {
            std::cout << "✓ SIP 200 OK processed successfully" << std::endl;
        } else {
            std::cerr << "✗ Failed to process SIP 200 OK" << std::endl;
            return 1;
        }
        
        std::cout << std::endl;
        std::cout << "3. Processing RTP packets..." << std::endl;
        
        // Create and process RTP packets
        std::vector<rtp::RtpPacket> packets = createCallRtpPackets();
        
        for (size_t i = 0; i < packets.size(); ++i) {
            const auto& packet = packets[i];
            
            if (packet.ssrc == 0x11111111) {
                // Alice to Bob
                correlator.processRtpPacket(packet, "*************", "*************", 49170, 38060);
            } else {
                // Bob to Alice
                correlator.processRtpPacket(packet, "*************", "*************", 38060, 49170);
            }
            
            std::cout << "  Processed RTP packet " << (i + 1) << "/" << packets.size() 
                     << " (SSRC=0x" << std::hex << packet.ssrc << std::dec << ")" << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "4. Correlation Results:" << std::endl;
        
        // Show correlation results
        auto correlations = correlator.getAllCorrelations();
        for (const auto& corr : correlations) {
            std::cout << "  Call ID: " << corr.call_id << std::endl;
            std::cout << "  SSRC: 0x" << std::hex << corr.ssrc << std::dec << std::endl;
            std::cout << "  Codec: " << corr.codec_name << std::endl;
            std::cout << "  Media Type: " << (corr.media_type == media::MediaType::AUDIO ? "Audio" : "Video") << std::endl;
            std::cout << "  Ports: " << corr.local_port << " -> " << corr.remote_port << std::endl;
            std::cout << "  IPs: " << corr.local_ip << " -> " << corr.remote_ip << std::endl;
            std::cout << std::endl;
        }
        
        std::cout << "5. Stream Statistics:" << std::endl;
        
        // Show stream statistics
        auto streams = rtp_engine.getAllStreams();
        for (auto* stream : streams) {
            auto stats = stream->getStats();
            std::cout << "  Stream ID: " << stats.stream_id << std::endl;
            std::cout << "  SSRC: 0x" << std::hex << stats.ssrc << std::dec << std::endl;
            std::cout << "  Payload Type: " << static_cast<int>(stats.payload_type) << std::endl;
            std::cout << "  Packet Count: " << stats.packet_count << std::endl;
            std::cout << "  Byte Count: " << stats.byte_count << std::endl;
            std::cout << "  Lost Packets: " << stats.lost_packets << std::endl;
            std::cout << std::endl;
        }
        
        std::cout << "6. Restoring call media..." << std::endl;
        
        // Restore the call
        std::string call_id = "a84b4c76e66710@*************";
        if (correlator.restoreCall(call_id, "./output")) {
            std::cout << "✓ Call media restored successfully" << std::endl;
        } else {
            std::cout << "! No media streams found for restoration" << std::endl;
        }
        
        std::cout << std::endl;
        std::cout << "🎉 SIP/RTP correlation demo completed successfully!" << std::endl;
        
        // Cleanup
        for (auto& packet : packets) {
            delete[] packet.payload;
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
