cmake_minimum_required(VERSION 3.14)

# project
project(yaLiteDpi LANGUAGES C CXX VERSION 1.0.24)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 11)

add_compile_options(-Wall -Wextra -Werror)

#
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
pkg_check_modules(glib2 REQUIRED IMPORTED_TARGET glib-2.0)

#
# engineNextDemo
#
add_executable(yaLiteDpi
  yaLiteDpi.cpp
)

target_link_libraries(yaLiteDpi
  PUBLIC yaEngineNext
  PUBLIC pcap
  PUBLIC PkgConfig::glib2
)

#
# plugins
#
add_subdirectory(plugins)
