#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_ringbuf.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <string.h>
#include <assert.h>
#include <arpa/inet.h>

#define PROTO_NAME "story"

typedef struct story_MessageHeader
{
    uint16_t magicCode;        // 固定为 "0xAB, 0xCD"
    uint16_t messageType;      // 网络字节序
    uint32_t messageLength;    // 消息长度(不包括 story_message_header)
    char     messageContent[];
} story_message_header_t;

typedef struct story_SessionUserdata
{
    int            storyFrameLen;
    nxt_ringbuf_t *rbuf;
} story_session_userdata_t;

#define SESSION_USERDATA_SIZE 6000

static
void story_uerdata_init(nxt_session_t *session _U_, void *userdata)
{
    story_session_userdata_t *u     = (story_session_userdata_t *)userdata;
    ya_allocator_t           *alloc = nxt_session_get_allocator(NULL, session);
    u->rbuf                         = nxt_ringbuf_create_wa(alloc, SESSION_USERDATA_SIZE);
}

static
void story_uerdata_finish(nxt_session_t *session _U_, void *userdata)
{
    story_session_userdata_t *u = (story_session_userdata_t *)userdata;
    ya_allocator_t           *alloc = nxt_session_get_allocator(NULL, session);
    nxt_ringbuf_destroy_wa(alloc, u->rbuf);
}

void reset_story_session_userdata(story_session_userdata_t *u)
{
    nxt_ringbuf_pop_front(u->rbuf, u->storyFrameLen);
    u->storyFrameLen   = 0;
}

int read_story_header(nxt_engine_t *engine, nxt_session_t *session, story_session_userdata_t *u)
{
    int gotLen = nxt_session_stream_rbread(engine, session, NXT_DIR_S2C, u->rbuf, sizeof(story_message_header_t), NULL);
    if (gotLen < 0 || (uint32_t)gotLen < sizeof (story_message_header_t))
    {
        return -1;
    }

    story_message_header_t *header = (story_message_header_t *)nxt_ringbuf_get_data(u->rbuf);
    header->messageLength = ntohl(header->messageLength);

    // 一个 frame 长度为: header_len + message_len;
    return header->messageLength + sizeof (story_message_header_t);
}

static
int dissector_dissect_story(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf _U_)
{
    story_session_userdata_t *u       = (story_session_userdata_t *)nxt_session_get_userdata(engine, session);

    // userdata 中没有记录下 story_content_len, 说明还没有读取到 header, 则需要先进行 header 读取
    if (u->storyFrameLen <= 0)
    {
        u->storyFrameLen = read_story_header(engine, session, u);
    }

    // header 读取失败(可能正好 header 被 tcp 分片截断), 将无法知道还需要读取多少长度的 story content
    if (u->storyFrameLen <= 0)
    {
        return NXT_DISSECT_ST_WANT_MORE_BYTES;
    }

    // 读取 story content, 长度为 u->story_content_len
    nxt_session_stream_rbread(engine, session, NXT_DIR_S2C, u->rbuf, 0, NULL); // 0 表示尽量读取
    if (nxt_ringbuf_get_data_length(u->rbuf) < (uint32_t)u->storyFrameLen)
    {
        return NXT_DISSECT_ST_WANT_MORE_BYTES;
    }

    // story 读取成功，将其装填到 precord 中进行 post
    story_message_header_t   *header  = (story_message_header_t *)nxt_ringbuf_get_data(u->rbuf);
    precord_t*  precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    assert(memcmp((char *)&header->magicCode, "ABCD", 4));

    precord_put(precord, "type",  uinteger, header->messageType);
    precord_put(precord, "len",   uinteger, header->messageLength);
    precord_put(precord, "story", stringn,  header->messageContent, header->messageLength);

    // post a precord
    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);
    nxt_session_destroy_record(engine, session, precord);

    // 读取完成一个完整消息，session userdata 归零
    reset_story_session_userdata(u);

    return nxt_mbuf_get_length(mbuf);
}

static
int dissector_schema_def_story(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "story");
    pschema_register_field(pschema, "type",   YA_FT_UINT16,  "message type");
    pschema_register_field(pschema, "len",    YA_FT_UINT32,  "story len");
    pschema_register_field(pschema, "story",  YA_FT_STRING,  "story content");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "story",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = dissector_schema_def_story,
    .dissectFun   = dissector_dissect_story,
    .userdata     = {sizeof (story_session_userdata_t), story_uerdata_init, story_uerdata_finish},
    .mountAt      = {
        NXT_MNT_NUMBER("tcp",  7777),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(story)
{
    nxt_dissector_register(&gDissectorDef);
}
