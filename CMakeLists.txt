# cmake version
cmake_minimum_required(VERSION 3.14)
find_package(yaBasicUtils REQUIRED)

# project
project(yaEngineNext LANGUAGES C CXX VERSION 0.0.40 DESCRIPTION "yview next generation protocol dissecting engine.")
set(YA_PROJECT_LIBRARY yaEngineNext)
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/cmake)

option(ENABLE_ASAN off)

#
# testing
#
# 配置 memcheck 不报告 'still reachable', 默认会误报 glib 存在泄漏
set(MEMORYCHECK_COMMAND_OPTIONS "-q --tool=memcheck --leak-check=full --num-callers=50 --trace-children=yes")
include(CTest)
enable_testing()

# generate compile_commands.json
set(CMAKE_EXPORT_COMPILE_COMMANDS on)

# variables
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 14)
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)

add_compile_options(-Wall -Wextra -Werror)
add_compile_options(-Wno-deprecated-declarations)
add_compile_options(-fno-omit-frame-pointer)

# 函数调用栈保护与栈溢出检测
add_compile_options(-fstack-protector)

# 开启 FORTIFY, 需要 -O
if (CMAKE_BUILD_TYPE STREQUAL "Release")
  add_compile_definitions(_FORTIFY_SOURCE=1)
endif()

# find boost
set(Boost_USE_STATIC_LIBS ON)
find_package(Boost 1.81.0 REQUIRED COMPONENTS container)

file(GLOB DISSECTOR_SOURCES ${CMAKE_SOURCE_DIR}/src/dissector/dissector_*.c)

#
# parsers 模块
#
add_subdirectory(src/parser)
add_subdirectory(src/util)

set(project_FILES
  src/c_api.cpp
  src/mbuf.cpp
  src/ringbuf.c
  src/engine.cpp
  src/session.cpp
  src/recognizer.cpp
  src/dissector.cpp
  src/tcp_flow.cpp
  $<TARGET_OBJECTS:util>
  $<TARGET_OBJECTS:parser>
  ${DISSECTOR_SOURCES}
)

add_library(${YA_PROJECT_LIBRARY} SHARED
  ${project_FILES}
)

#
# include directories
#
target_include_directories(${YA_PROJECT_LIBRARY} PRIVATE include) # 将 include 传递给库的使用方
target_include_directories(${YA_PROJECT_LIBRARY} INTERFACE
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"        # 表示当前项目目录下的 include 目录
  "$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>"              # 表示 include 安装目录，通常为 /usr/local/include
)

#
# definitions
#
target_compile_definitions(${YA_PROJECT_LIBRARY} PRIVATE ENGINE_NEXT_CORE) # 区分当前位于'引擎' 还是'插件', 用于差异化 NXT_DISSECTOR_INIT 宏的定义

# import libraries
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
pkg_check_modules(glib2 REQUIRED IMPORTED_TARGET glib-2.0)
pkg_check_modules(hs REQUIRED IMPORTED_TARGET libhs)
pkg_check_modules(YA_PRECORD REQUIRED IMPORTED_TARGET libyaProtoRecord)

#
# depend libraries
#
target_link_libraries(${YA_PROJECT_LIBRARY}
  PRIVATE PkgConfig::hs
  PRIVATE Boost::container
  PUBLIC  ${YA_PRECORD_STATIC_LDFLAGS}
  PUBLIC  PkgConfig::glib2
  PUBLIC  pthread
)

set(LIBRARY_PUBLIC_HEADERS
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_engine.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_mbuf.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_ringbuf.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_parser.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_parser_core.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_parser_basic.rl
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_dissector.h
  ${CMAKE_SOURCE_DIR}/include/yaEngineNext/nxt_export.h
)

#
# library properties
#
# set_target_properties(${YA_PROJECT_LIBRARY} PROPERTIES POSITION_INDEPENDENT_CODE ON)
set_target_properties(${YA_PROJECT_LIBRARY} PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/lib
    PUBLIC_HEADER "${LIBRARY_PUBLIC_HEADERS}"         # 当心，这里必须使用 "", 才能表达一个 cmake list
)

#
# clang-tidy, 静态分析，目前仅进行编码规范检查(例如函数、类命名等)
#
add_custom_target(clang-tidy
  COMMAND run-clang-tidy -header-filter=.* -quiet -checks="-*,readability-identifier-naming" -p ${CMAKE_BINARY_DIR}
  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
)

#
# install, must include GUNInstallDirs before any install command.
#
include(GNUInstallDirs)

#
# install lib, header file.
#
install(TARGETS ${YA_PROJECT_LIBRARY}
  EXPORT yaEngineNextTarget
  LIBRARY                                     # => /usr/local/lib64
  PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/${YA_PROJECT_LIBRARY}
)

install(EXPORT yaEngineNextTarget
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${YA_PROJECT_LIBRARY}
  NAMESPACE yaEngineNext::
)

install(FILES "cmake/yaEngineNextConfig.cmake"
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${YA_PROJECT_LIBRARY}
)

#
# make package
#
ya_make_package(libyaEngineNext)

#
# test libyaEngineNext
#
if(EXISTS ${CMAKE_SOURCE_DIR}/test/CMakeLists.txt)
  add_subdirectory(test)
endif()
