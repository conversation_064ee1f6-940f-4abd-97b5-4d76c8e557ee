## libyaEngineNext (yview next generation protocol dissecting engine.)
   engineNext 解析引擎，尽可能简化协议开发流程，一个协议解析器只需要关心自身必要的解析工作;

## 设计目标
   * engineNext 不假定固定的协议层次结构，dissector 只需要注册(挂载 mount_at)到一个已经存在的 dissector 中的某 next 域字段上，
     如 eth.type, ipv4.proto 等，称为 handoff 机制; engineNext 根据 handoff-table 自动进行协议层级的递进解析;
   * engineNext 将支持内置的承载层协议解析器如 eth, ipv4, ipv6, tcp, udp, gtp, mpls 等，以支持应用层协议挂载 handoff;
   * engineNext 不打算成为一个大而全的协议解析库，libyaEnginew 已经支持的协议 engineNext 并不计划内置支持，
     但常见的 http, tls, dns 因为性能原因会在 engineNext 中重新实现;
   * 支持丰富的扩展方式:
     - C 模块(插件): 注册解析器，handoff, 识别，解析; 需要操作字符串、指针、内存分配，容易出错;
     - ragel: 使用 ragel 生成 C 模块, 开发工作在 ragel 语言上;
     - pdl-struct: 使用 pdl 解释器解释执行 pdl 代码完成解析;
     - pdl-text: 使用 bnf 语法描述协议结构;
   * 基于 engine 建立的解析工作流，使用多个 engine 并行分担处理大流量;
   * 支持丰富的事件监听器，开放内部工作状态, 如会话建立、会话销毁、协议消息解析完成、包丢弃、遇到未知协议 packet;
   * 可加载 python(或 lua) 脚本来执行事件处理;
   * 提供 python binding 接口，可被 python 应用程序作为模块使用其解析功能，加载 pdl 脚本完成新协议解析，再通过事件处理完成业务功能；
   * 典型的工作模式: yapp 将特定流量分流给不同业务 app(python), 业务 app 各自使用 engineNext 完成特定协议的
     解析(解析大华私有协议，得到 h264 帧)与应用工作(将 h264 通过 rtp 打包转发到某流平台);

## 构建步骤
### 安装 yum 依赖
   * libyaBasicUtils 版本需要 >= 0.1.9;

```shell
yumi install libyaBasicUtils libyaFtypes-develop libyaProtoRecord-develop libpcap-devel \
ragel yaHyperscan \
google-benchmark-devel yaGtest-1.10.0
# 如果已经安装了低版本 gtest, 请先卸载: yumi remove gtest gtest-devel
```

### 安装 boost
```shell
wget http://192.168.101.22/archive/boost_1_86_0.tar.gz
tar xf boost_1_86_0.tar.gz
cd boost_1_86_0
./bootstrap.sh
./b2 -d+2 --with-container cxxflags="-fPIC" cflags="-fPIC" link=static variant=release install
```

### 构建
```shell

# 以下假定当前目录为项目根目录:
cmake -S . -B build
cmake --build build

# 以下假定当前目录为 build:
# 构建 debug 版本:
make debug
make debug asan=on    # 开启 asan 检查，建议使用高版本 gcc(例如使用 scl);

# 构建 release 版本:
make release
make release asan=on  # 开启 asan 检查;

# 运行单元测试(还需要注意在开启 asan 版本下运行):
make unit

# 运行单元测试并使用 valgrind 进行检查(此时不能开启 asan):
make unit wrapper='valgrind --tool=memcheck'

# 运行单元测试并使用 gdb 调试:
make unit wrapper=gdb

# 运行功能测试(还需要注意在开启 asan 版本下运行)
make test

# 执行 clang-tidy 静态代码分析(目前仅进行编码规范检查):
make clang-tidy

# 使用 valgrind 对'功能测试'程序进行内存检查:
cmake --build build -t memcheck

# 安装 engineNext 库:
cmake --build build -t install
```

## 开发原则
- 所有新添加的功能(对外接口、内部类、内部函数)，需要有单元测试进行覆盖;
- 当发现了某个 bug 需要添加单元测试进行对应，通过单元测试重现，再修复，并一直保留它;
- 所有的提交都需要确保通过全部单元测试, 并且在开启 asan 检查版本中没有报告内存问题; make debug asan=on && make unit
- 所有的提交都需要确保通过全部功能测试，并且在开启 asan 检查版本中没有报告内存问题; make debug asan=on && make test
- 所有的提交都需要遵守 .clang-tidy 中制定的编码规范; make clang-tidy

## 编码规范
见 doc/coding_style.org

## engineNext 示例程序 yaLiteDpi 运行
```shell
./bin/yaLiteDpi -i ens161
```

## engineNext 的使用与插件示例
test/yaLiteDpi
插件示例: test/yaLiteDpi/plugins/dissector_dns.c

## 独立插件开发示例:
http://192.168.20.98/test/testyaEngineNext.git
