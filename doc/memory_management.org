#+TITLE: memory_management
#+AUTHOR: zhengsw
#+DATE: 2024.10.16

* 概述
  内存管理对性能与程序正确性有较大的影响，engineNext 将提供相应的组件来改进以下问题:
  - 内存泄漏; 遵循 engineNext 内存管理开发规范: 避免直接使用 new/malloc 分配内存，使用 session->alloc 等; 使用引用计数;
  - 内存分配与释放耗时; 选择使用内存池、对象池等技术;
  - 对象拷贝耗时; 选择使用引用计数技术;
  - 内存读写慢; 使用由巨页分配的内存(待实现);

* 对象创建技术性能排名(由低到高)
  - malloc/new
  - arena
  - 内存池
  - 对象池

* engineNext 运行时需要管理的对象
 | 对象                 | 生命周期级别   | 规模 | 是否固定大小 | Creator 分类 | Creator 位于 | 说明                          |
 |----------------------+----------------+------+--------------+--------------+--------------+-------------------------------|
 | nxt_session_t        | session        |    1 | 固定         | 内存池       | engine       |                               |
 | nxt_TcpSegmentNode_t | session        |    n | 固定         | 对象池       | session      |                               |
 | precord_t            | session/packet |    n | 不固定       | 对象池       | engine       |                               |
 | nxt_timer_t          | session        |    1 | 固定         | 暂不处理     |              |                               |
 | nxt_ringbuf_t        | session        |    1 | 固定         | 暂不处理     |              |                               |
 | nxt_mbuf_t           | packet         |    1 | 不固定       | 对象池       | session      | mbuf 需要是定长的，并且是链式 |
 | precord_layer_t      | layer          |    1 | 不固定       | 对象池       | precord      |                               |
 | ya_fvalue_t          | field          |    1 | 不固定       | 对象池       | layer        |                               |

* 框架中的内存管理
** 原则：
   - 将对象的创建与销毁操作进行封装，使用 Creator<T> 来为 T 类型创建与销毁对象;
   - packet 及以下级别的对象创建与销毁必须足够快，需要使用对象池;

* 插件中的内存管理
