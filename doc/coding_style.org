#+TITLE: coding_guideline
#+AUTHOR: zhengsw
#+DATE: 2025/03/06

* 命名风格一览
  | 分类             | 大小写     | 前缀       | 后缀      | 示例 | clang-tidy 检查 | 核查 | 说明 |
  |------------------+------------+------------+-----------+------+-----------------+------+------|
  | 结构体/类        | CamelCase  | nxt_(可选) |           |      | 不支持可选前缀  | 完成 |      |
  | 枚举             | lower_case |            |           |      | lower_case      | 完成 |      |
  | 枚举项           | UPPER_CASE |            |           |      | UPPER_CASE      | 完成 |      |
  |------------------+------------+------------+-----------+------+-----------------+------+------|
  | 宏               | UPPER_CASE |            |           |      | UPPER_CASE      | 完成 |      |
  | 类成员方法       | camelBack  |            |           |      | 支持            | 完成 |      |
  | 类成员变量       | camelBack  |            | _         |      | 支持            | 完成 |      |
  | 全局变量         | CamelCase  | g          |           |      | 支持            | 完成 |      |
  | 静态变量         | CamelCase  | s          |           |      | 支持            | 完成 |      |
  | 常量             | CamelCase  | k          |           |      | 支持            | 完成 |      |
  | 类 static 变量   | CamelCase  | c          |           |      | 支持            | 完成 |      |
  | 局部变量         | camelBack  |            |           |      | 支持            | 完成 |      |
  | 函数参数         | camelBack  |            |           |      | 支持            | 完成 |      |
  | typedef          | lower_case |            | _t, _enum |      | 未检查后缀      | 完成 |      |
  | 函数             | lower_case |            |           |      | 支持            | 完成 |      |
  | 回调函数 typedef |            |            | _fun      |      |                 |      |      |

* 命名规范
** 文件
   - 文件命名统一采用'全小写字段 + 下划线' 形式;
   - include 头文件以项目缩写为前缀，即 'nxt_'
   - 单元测试以 '_test' 为后缀，dissector_http.c 为 http 解析器实现，dissector_http_test.cpp 为其对应的单元测试；
   - 插件 so 命名为: yaNxtDissector_xxx.so
** 结构体(不在 clang-tidy 中限定)
   - [X] CamelCase;
** 宏
   - [X] "_"隔开的由全大写字母组成的单词;
** 枚举
   - [X] 大骆驼;
** typedef
   - [X] snake_case, struct/class _t, enum: _enum;
** 函数
   - [X] C 函数(全局函数): "_"隔开的由全小写字母组成的单词;
** 变量
   - [X] 函数参数: 小骆驼;
   - [X] 局部变量: 小骆驼;
   - [X] 全局变量: 大骆驼，前缀为 "g";
   - [X] 静态变量: 大骆驼，前缀为 "s";
** 常量
   - [X] const 常量: 大骆驼，前缀"k";
** 类
   - [X] 类名：大骆驼，前缀为"nxt_"; 例如: nxt_Engine, nxt_PacketScope;
   - [X] private 成员变量: 小骆驼，后缀为"_";
   - [X] private 成员函数：小骆驼;
   - [X] public 成员变量: 小骆驼;
   - [X] public 成员方法: 小骆驼;
   - [X] 类成员变量: 大骆驼，后缀为"c";

* 开发规范
** 头文件包含次序
   - 头文件应该自给自足：头文件 foo.h 中使用到的类型、函数在本头文件中应该已经自己包含过了；使用方只需要 include "foo.h" 即可，不用担心依赖问题;
   - 头文件包含时应该按照'由亲到疏' 的次序进行排列: 这是对'头文件自给自足'原则的检测;
