#ifndef NXT_NTOH_H
#define NXT_NTOH_H

#include <arpa/inet.h>

static inline
uint64_t nxt_util_ntoh64(uint64_t net64)
{
    return ((uint64_t)ntohl(net64 & 0xFFFFFFFF) << 32) | ntohl(net64 >> 32);
}

template <int size, typename T>
static inline
T nxt_util_ntoh(T value);

template <>
inline
uint16_t nxt_util_ntoh<2, uint16_t>(uint16_t value)
{
    return ntohs(value);
}

template <>
inline
uint32_t nxt_util_ntoh<4, uint32_t>(uint32_t value)
{
    return ntohl(value);
}

template <>
inline
uint64_t nxt_util_ntoh<8, uint64_t>(uint64_t value)
{
    return nxt_util_ntoh64(value);
}

#endif /* NXT_NTOH_H */
