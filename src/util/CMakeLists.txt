# cmake version
cmake_minimum_required(VERSION 3.14)
set(YA_PROJECT_LIBRARY util)

add_library(${YA_PROJECT_LIBRARY} OBJECT
  to_str.c
  timer_impl_itimer.cpp
  itimer.c
)

set_target_properties(${YA_PROJECT_LIBRARY} PROPERTIES POSITION_INDEPENDENT_CODE ON)

set_source_files_properties(itimer.c PROPERTIES COMPILE_OPTIONS
  "-Wno-sign-compare;-Wno-unused-function;-Wno-unused-variable;-Wno-unused-parameter"
)

target_include_directories(${YA_PROJECT_LIBRARY} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}) # 当前项目的 SOURCE_DIR, 指 utils
target_include_directories(${YA_PROJECT_LIBRARY} PRIVATE ${CMAKE_SOURCE_DIR}/include) # 顶层项目下的 include 目录，指 libyaEngineNext/include 目录

