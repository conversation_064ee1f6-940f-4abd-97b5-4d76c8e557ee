#ifndef EXCEPTION_H
#define EXCEPTION_H

#include <string>
#include <exception>
#include "yaEngineNext/nxt_engine.h"

static inline
const char* nxt_exception_to_string(nxt_exception_enum except)
{
#define NXT_EXCEPT_CASE(e) case e: { return #e; }

    switch (except)
    {
        NXT_EXCEPT_CASE(NXT_EXCEPT_MBUF_ACCESS_OUT_OF_RANGE);
        NXT_EXCEPT_CASE(NXT_EXCEPT_MALFORMED_PACKET);
        NXT_EXCEPT_CASE(NXT_EXCEPT_DISSECTOR_ERROR);
        NXT_EXCEPT_CASE(NXT_EXCEPT_REASSEMBLE_ERROR);

    default:
        return "exception unknown";
    }
}

class nxt_Exception: public std::exception
{
public:
    nxt_Exception(nxt_exception_enum type, const char *msg)
        : type_(type)
    {
        msg_.append(nxt_exception_to_string(type_));
        msg_.append(": ");
        msg_.append(msg);
    }

public:
    virtual const char *what() const noexcept (true) override
    {
        return msg_.c_str();
    }

private:
    nxt_exception_enum type_;
    std::string        msg_;
};

#endif /* EXCEPTION_H */
