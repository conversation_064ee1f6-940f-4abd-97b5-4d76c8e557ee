#include "tcp_flow.h"
#include "engine.h"
#include "session.h"
#include "util/creator.hpp"
#include "yaEngineNext/nxt_util.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/allocator.hpp>

#define NXT_TCP_REASS_DISABLE_LOG   1
#define NXT_TCP_REASS_TRACE_QUEUE   0
#define NXT_TCP_REASS_DROP_PURE_ACK 1

#if NXT_TCP_REASS_DISABLE_LOG
#undef LOG_DBG
#define LOG_DBG(...)
#endif

nxt_TcpSegmentQueue::nxt_TcpSegmentQueue(nxt_session_t *session)
    : head_(yv::create_object<nxt_TcpSegmentNode>(session->getAllocator()))
    , session_(session)
{
    assert(head_ != NULL);
}

nxt_TcpSegmentQueue::~nxt_TcpSegmentQueue()
{
    nxt_TcpSegmentNode *p = head_->getNext();
    nxt_TcpSegmentNode *q = NULL;
    while (p != head_)
    {
        q = p;
        p = p->getNext();
        destroyTcpSegmentNode(q);

#ifndef NDEBUG
        length_--;
#endif
    }

    assert(length_ == 0); // 确保 node 全部被销毁;

    yv::destroy_object<nxt_TcpSegmentNode>(session_->getAllocator(), head_);
}

inline
nxt_TcpSegmentNode* nxt_TcpSegmentQueue::createTcpSegmentNode(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t len)
{
    // clone a fresh mbuf for enqueue;
    nxt_mbuf_t *mbufClone = yv::create_object<nxt_Mbuf>(session_->getAllocator(), *mbuf);
    nxt_TcpSegmentNode *s = yv::create_object<nxt_TcpSegmentNode>(session_->getAllocator(), mbufClone, seq, len);
    return s;
}

inline
void nxt_TcpSegmentQueue::destroyTcpSegmentNode(nxt_TcpSegmentNode *node)
{
    yv::destroy_object<nxt_Mbuf>(session_->getAllocator(), node->getMbuf());
    yv::destroy_object<nxt_TcpSegmentNode>(session_->getAllocator(), node);
}

int nxt_TcpSegmentQueue::enqueue(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t len)
{
    nxt_TcpSegmentNode *s = createTcpSegmentNode(mbuf, seq, len);

    // 将 s 插入到尾部，也就是作为 head 的 prev;
    return this->enqueue(s);                                // MEM-move: 交给 queue 管理 segment;
}

int nxt_TcpSegmentQueue::enqueue(nxt_TcpSegmentNode *segment)
{
    head_->linkPrev(segment);
    length_ += 1;
    totalBytes_ += segment->getLen();
    return 0;
}

nxt_TcpSegmentNode* nxt_TcpSegmentQueue::peek()
{
    if (this->empty())
    {
        return NULL;
    }

    return head_->getNext();
}

nxt_TcpSegmentNode* nxt_TcpSegmentQueue::dequeue()
{
    nxt_TcpSegmentNode *s = this->peek();
    if (NULL == s)
    {
        return NULL;
    }

    s->unlink();
    length_ -= 1;
    totalBytes_ -= s->getMbuf()->getLength();
    return s;
}

void nxt_TcpSegmentQueue::showQueue(const char *queueName _U_)
{
    LOG_DBG("==========show Queue:%s begin, head:%p, len:%u, totalBytes:%u", queueName, this->head_, this->getQueueLength(), this->getQueueTotalBytes());
    nxt_TcpSegmentNode *p = head_->getNext();

    uint32_t loopCnt = 0;
    while (p != head_)
    {
        // 检测是否在循环链表中出现了"小环";
        if (loopCnt++ > this->length_)
        {
            assert(false);
        }

        LOG_DBG("(segment:%p, seq:%u, len:%u, next_seq:%u, mbuf:%p, mbuf_len:%d, prev:%p, next:%p)", p, p->getSeq(), p->getLen(),
                p->getSeq() + p->getLen(),
                p->getMbuf(), p->getMbuf()->get_length(),
                p->getPrev(), p->getNext());
        p = p->getNext();
    }
    LOG_DBG("==========show Queue:%s end.", queueName);
}

// 考虑以下场景能否正确处理:
// [X] 队列为空时，第一个 fresh segment;
// [X] 队列中只有 1 个 segment, fresh 需要插入到队头; prev 为 head, next 为第一结点, 不用检测 fresh 与 prev 重叠;
//     需要检测 fresh 与 next 重叠; fresh 插入到 next 的前面，所以 fresh 成为新的第一结点;
// [X] 队列中只有 1 个 segment, fresh 需要插入到队尾; prev 为第一结点, next 为 head, 检测 fresh 与 prev 重叠，
//     不用检测 fresh 与 next 重叠; fresh 插入到 next 的前面，也就是成为了新的最尾 segment;
// [X] 列队中有超过 1 个 segment, fresh 需要插入到中间位置; 队头? 队尾?
int nxt_TcpReassQueue::enqueue(nxt_mbuf_t *mbuf, uint32_t freshSeq, uint16_t payloadLen)
{
    nxt_TcpSegmentNode *fresh = createTcpSegmentNode(mbuf, freshSeq, payloadLen);

    // 队列为空
    if (this->empty())
    {
        return nxt_TcpSegmentQueue::enqueue(fresh);                   // MEM-move: 交给 queue 管理 segment;
    }

    // 在队列中从后往前寻找，找到第一个 seq 比 fresh 的 seq 小的 segment p, p 的 next 为 n;
    nxt_TcpSegmentNode *p = head_->getPrev();
    uint32_t loopCnt _USED_ON_DEBUG_ = 0;
    while (p != head_)
    {
#if NXT_TCP_REASS_TRACE_QUEUE
        LOG_DBG("seq cmp:head:%p, p:%p, p-seq:%u, n-seq:%u", head_, p, p->getSeq(), seq);
#endif
        if (SEQ_LT(p->getSeq(), freshSeq))
        {
            LOG_DBG("tcp-reass:find fresh segment's position to insert, loop cnt:%d", loop_cnt);
            break;
        }

#ifndef NDEBUG
        // 防止死循环
        if (loopCnt++ > this->length_)
        {   // 链表出现不经过 head 的小环，出错;
            assert(false);
            break;
        }
#endif

        p = p->getPrev();
    }

    nxt_TcpSegmentNode *next = p->getNext();
    nxt_TcpSegmentNode *prev = p;

    // 如果 prev 不为 head_ 说明存在有效的 prev, 需要检测'fresh 与 prev 的重叠'
    if (prev != head_)
    {
        int diff =  prev->getNextSeq() - fresh->getSeq();
        if (diff >= fresh->getLen())
        {   // 重叠部分超过了整个 fresh 长度，fresh 的内容在 prev 中均存在，舍弃 fresh
            LOG_DBG("tcp-reass: drop duplicate segment: seq:%u, len:%u", fresh->getSeq(), fresh->getLen());
            this->destroyTcpSegmentNode(fresh);
            return 0;
        }

        if (diff > 0)
        {   // prev 与 fresh 部分重叠，从 fresh 头部裁剪掉重叠的部分;
            fresh->trimHead(diff);
        }
    }

    // 检测 fresh 与 next(以及其后续) 是否有重叠
    while (next != head_)
    {
        int diff = fresh->getNextSeq() - next->getSeq();
        if (diff <= 0)
        {   // fresh 与 next 无重叠
            break;
        }

        if (diff < next->getLen())
        {   // fresh 与 next 有重叠，但是重叠部分长度小于 next 的长度，对 next 的 head 进行裁剪
            next->trimHead(diff);
            break;
        }

        // next 整个都在 fresh 中存在，将 fresh 从 queue 中移除
        nxt_TcpSegmentNode *totalOverlapped = next; // 记录 '完全重叠' node;
        next = next->getNext();                     // 先调整 next, 让循环继续

        totalOverlapped->unlink();                  // 从队列中的移除完全重叠的 node;
        this->destroyTcpSegmentNode(totalOverlapped);
        this->length_--;                            // 更新 length 计数;

        LOG_DBG("tcp-reass: overlapped packet.");
    }

    // 将 fresh 添加到 next 之前
    next->linkPrev(fresh);
    this->length_++;
    this->totalBytes_ += fresh->getLen(); // TODO: 如果发生裁剪，需要调整 queue 的增长量;
    return 0;
}

int nxt_TcpFlow::processPacket(nxt_engine_t *engine, nxt_mbuf_t *mbuf)
{
    const nxt_TcpHdr *th         = (const nxt_TcpHdr*)mbuf->getRaw(0);
    uint32_t          seq        = nxt_mbuf_get_uint32_ntoh(mbuf, 4);
    uint16_t          headerLen  = th->off << 2;
    uint16_t          payloadLen = nxt_mbuf_get_length(mbuf) - headerLen;

#if NXT_TCP_REASS_DROP_PURE_ACK
    if (th->flags == TH_ACK && payloadLen == 0)     // pure ack
    {
        return 0;
    }
#endif

TCP_FSM_REDO:
    // 根据当前状态进行动作
    switch (state_)
    {
    case NXT_TCP_NEWLY:
    {
        if (NXT_TCP_CHECK_FLAG_SET(th->flags, TH_SYN))
        {   // 首先遇到 sync 帧，迁移到 NXT_TCP_ESTABLISHED 状态
            nextExpectSeq_ = seq + 1;      // nextExpectSeq 初始化为 sync seq + 1;
            state_ = NXT_TCP_ESTABLISHED;
            goto TCP_FSM_REDO;
        }

        if (NXT_TCP_CHECK_FLAG_SET(th->flags, TH_FIN))
        {   // 首先遇到 fin 帧，乱序太严重了, 暂不动作
            break;
        }

        // 首先遇到数据帧，进入 NXT_TCP_ESTABLISHED_BUT_NO_SYNC 状态
        state_ = NXT_TCP_ESTABLISHED_BUT_NO_SYNC;
        goto TCP_FSM_REDO;
    }

    case NXT_TCP_ESTABLISHED_BUT_NO_SYNC:
    {
        if (NXT_TCP_CHECK_FLAG_SET(th->flags, TH_SYN))
        {   // 遇到 sync 帧，迁移到 NXT_TCP_ESTABLISHED 状态
            nextExpectSeq_ = seq + 1;      // nextExpectSeq 初始化为 sync seq + 1;
            state_ = NXT_TCP_ESTABLISHED;
            goto TCP_FSM_REDO;
        }

        if (NXT_TCP_CHECK_FLAG_SET(th->flags, TH_FIN))
        {   // 遇到 fin 帧，乱序太严重了, 暂不动作
            break;
        }

        // 数据帧
        return this->enqueueReass(engine, mbuf, seq, headerLen, payloadLen);
    }

    case NXT_TCP_ESTABLISHED:
    {
        if (NXT_TCP_CHECK_FLAG_SET(th->flags, TH_FIN))
        {   // 遇到 fin 帧，仅应该标记，因为有可能它是乱序到达的；除非它就是 nextExpectSeq;
#ifndef NDEBUG
            char t5ReprBuff[60];
            nxt_util_t5ipv4_to_str(&t5_, t5ReprBuff, sizeof t5ReprBuff);
            LOG_DBG("tcp-finish: %s", t5_repr_buff);
#endif
            // TODO: 进行事件通知
        }

        // 数据帧
        return this->enqueueReass(engine, mbuf, seq, headerLen, payloadLen);
    }

    default:
    {
        break;
    }
    }

    return 0;
}

int nxt_TcpFlow::enqueueReass(nxt_engine_t *engine, nxt_mbuf_t *mbuf, uint32_t seq, uint16_t headerLen, uint16_t payloadLen)
{
    nxt_mbuf_range_adjust(mbuf, headerLen, 0); // mbuf range 调整为从 tcp payload 开始;

    reassQueue_.enqueue(mbuf, seq, payloadLen);
    int commitBytes = this->checkReassedToCommit(engine);

#if NXT_TCP_REASS_TRACE_QUEUE
    reassQueue_.showQueue("reass");
    commitQueue_.showQueue("commit");
#endif
    return commitBytes;
}

int nxt_TcpFlow::checkReassedToCommit(nxt_engine_t *engine _U_)
{
    if (state_ != NXT_TCP_ESTABLISHED)
    {   // 如果没有进入到 established 状态，说明没有遇到 sync 也就没有正确的 nextExpectSeq_;
        return -1;
    }

    int commitBytes = 0;
    while (!reassQueue_.empty())
    {
        // 当 reassQueue_ 队头 segment 长度为 0(例如 sync) 应该直接让它 commit;
        // 但当 reassQueue_ 队头是一个数据帧，它必须等于 nextExpectseq_ 才提交;
        if (reassQueue_.peek()->getLen() > 0 && reassQueue_.getStartSeq() != this->nextExpectSeq_)
        {
            break;
        }

        nxt_TcpSegmentNode *commitS = reassQueue_.dequeue();      // MEM-dettach: segment 从 reassQueue_ 中移除
        {
            nxt_tcp_segment_t segment;
            commitS->copyToSegment(&segment);

            nxt_mbuf_range_t range = nxt_mbuf_range_set(segment.mbuf, 0, nxt_mbuf_get_capacity(segment.mbuf));
            engine->fireTcpEvent(&segment);
            nxt_mbuf_range_set(segment.mbuf, range.begin, range.end);
        }
        commitQueue_.enqueue(commitS);                            // MEM-move: 交给 commit queue 管理 segment;
        commitBytes += commitS->getLen();
        this->nextExpectSeq_ += commitS->getLen();
    }

    return commitBytes;
}

int nxt_TcpFlow::read(uint8_t *buff, uint32_t buffLen, nxt_stream_read_res_t *readStatus _U_)
{
    if (state_ != NXT_TCP_ESTABLISHED)
    {   // 如果没有进入到 established 状态，说明没有遇到 sync 也就没有正确的 nextExpectSeq_;
        return 0;
    }

    return commitQueue_.read(buff, buffLen);
}

int nxt_TcpFlow::readToRingbuf(nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus _U_)
{
    if (state_ != NXT_TCP_ESTABLISHED)
    {   // 如果没有进入到 established 状态，说明没有遇到 sync 也就没有正确的 nextExpectSeq_;
        return 0;
    }

    return commitQueue_.readToRingbuf(rbuf, readLen);
}
