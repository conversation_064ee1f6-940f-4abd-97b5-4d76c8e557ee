#include "mbuf.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/allocator.hpp>
#include <assert.h>
#include <arpa/inet.h>
#include <stdlib.h>
#include <string.h>

nxt_mbuf_t* nxt_mbuf_new_blank_wa(ya_allocator_t* alloc, const uint32_t buffSize)
{
    // WARNING: 这里没有写错参数，这里需要调用构造函数: nxt_Mbuf::nxt_Mbuf(ya_allocator_t *alloc, uint32_t buffSize)
    // 第一个 alloc 是 yv::create_object 用于创建 nxt_Mbuf 对象内存的分配器，第二个是 nxt_Mbuf::nxt_Mbuf 构造函数的第一个参数;
    return yv::create_object<nxt_Mbuf>(alloc, alloc, buffSize);
}

nxt_mbuf_t* nxt_mbuf_new_by_copy_wa(ya_allocator_t* alloc, const uint8_t *data, uint32_t dataLen)
{
    // WARNING: 这里没有写错参数，这里需要调用构造函数: nxt_Mbuf::nxt_Mbuf(ya_allocator_t *alloc, uint32_t buffSize)
    // 第一个 alloc 是 yv::create_object 用于创建 nxt_Mbuf 对象内存的分配器，第二个是 nxt_Mbuf::nxt_Mbuf 构造函数的第一个参数;
    nxt_mbuf_t *mbuf = yv::create_object<nxt_Mbuf>(alloc, alloc, dataLen);
    nxt_mbuf_memcpy_in(mbuf, 0, data, dataLen);

    return mbuf;
}

nxt_mbuf_t* nxt_mbuf_new_by_ref_wa(ya_allocator_t* alloc, const uint8_t *buff, uint32_t buffSize)
{
    return yv::create_object<nxt_Mbuf>(alloc, (uint8_t *)buff, buffSize, 0, buffSize);
}

nxt_mbuf_t* nxt_mbuf_new_clone_wa(ya_allocator_t* alloc, nxt_mbuf_t *mbuf)
{
    return yv::create_object<nxt_Mbuf>(alloc, *mbuf);
}

void nxt_mbuf_free_wa(ya_allocator_t *alloc, nxt_mbuf_t *mbuf)
{
    yv::destroy_object(alloc, mbuf);
}

int nxt_mbuf_memcpy_in(nxt_mbuf_t *mbuf, int offset, const uint8_t *bytes, uint32_t len)
{
    return mbuf->memcpyIn(offset, bytes, len);
}

int nxt_mbuf_strcpy_in(nxt_mbuf_t *mbuf, int offset, const char *str)
{
    return mbuf->strcpyIn(offset, str);
}

int nxt_mbuf_strncpy_in(nxt_mbuf_t *mbuf, int offset, const char *str, uint32_t len)
{
    mbuf->memcpyIn(offset, (const uint8_t*)(str), len);
    return len ;
}

int nxt_mbuf_advance(nxt_mbuf_t *mbuf, uint32_t len)
{
    return mbuf->rangeAdjust(len, 0).begin;
}

nxt_mbuf_range_t nxt_mbuf_range_tell(nxt_mbuf_t *mbuf)
{
    return mbuf->rangeTell();
}

nxt_mbuf_range_t nxt_mbuf_range_set(nxt_mbuf_t *mbuf, int begin, int end)
{
    return mbuf->rangeSet(begin, end);
}

nxt_mbuf_range_t  nxt_mbuf_range_reset(nxt_mbuf_t *mbuf)
{
    return mbuf->rangeSet(0, mbuf->getCapacity());
}

nxt_mbuf_range_t nxt_mbuf_range_adjust(nxt_mbuf_t *mbuf, int beginDiff, int endDiff)
{
    return mbuf->rangeAdjust(beginDiff, endDiff);
}

int nxt_mbuf_get_length(nxt_mbuf_t *mbuf)
{
    return mbuf->getLength();
}

int nxt_mbuf_get_capacity(nxt_mbuf_t *mbuf)
{
    return mbuf->getCapacity();
}

const uint8_t* nxt_mbuf_get_raw(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getRaw(offset);
}

const uint8_t*nxt_mbuf_get_bytes(nxt_mbuf_t *mbuf, uint32_t offset, uint32_t len _U_)
{
    return mbuf->getBytes(offset, len);
}

const char* nxt_mbuf_get_string(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return (const char *)nxt_mbuf_get_raw(mbuf, offset);
}

uint8_t nxt_mbuf_get_uint8(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<uint8_t>(offset);
}

uint16_t nxt_mbuf_get_uint16(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<uint16_t>(offset);
}

uint32_t nxt_mbuf_get_uint32(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<uint32_t>(offset);
}

uint64_t nxt_mbuf_get_uint64(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<uint64_t>(offset);
}

uint64_t nxt_mbuf_get_uint64_n(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len)
{
    return mbuf->getIntegerAsSizeOfN<uint64_t>(offset, len);
}

int8_t nxt_mbuf_get_int8(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<int8_t>(offset);
}

int16_t nxt_mbuf_get_int16(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<int16_t>(offset);
}

int32_t nxt_mbuf_get_int32(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<int32_t>(offset);
}

int64_t nxt_mbuf_get_int64(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger<int64_t>(offset);
}

int64_t nxt_mbuf_get_int64_n(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len)
{
    return mbuf->getIntegerAsSizeOfN<int64_t>(offset, len);
}

uint16_t nxt_mbuf_get_uint16_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint16_t>(offset);
}

uint32_t nxt_mbuf_get_uint32_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint32_t>(offset);
}

uint64_t nxt_mbuf_get_uint64_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint64_t>(offset);
}

uint64_t nxt_mbuf_get_uint64_n_ntoh(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len)
{
    return mbuf->getIntegerAsSizeOfN_ntoh<uint64_t>(offset, len);
}

// TODO: 以下 signed integer 版本 ntoh 可能有隐患，以后需要仔细考察;
int16_t nxt_mbuf_get_int16_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint16_t>(offset);
}

int32_t nxt_mbuf_get_int32_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint32_t>(offset);
}

int64_t nxt_mbuf_get_int64_ntoh(nxt_mbuf_t *mbuf, uint32_t offset)
{
    return mbuf->getInteger_ntoh<uint64_t>(offset);
}

int64_t nxt_mbuf_get_int64_n_ntoh(nxt_mbuf_t *mbuf, uint32_t offset, uint8_t len)
{
    return mbuf->getIntegerAsSizeOfN_ntoh<uint64_t>(offset, len);
}

nxt_mbuf_t* nxt_mbuf_new_blank(const uint32_t buffSize)
{
    return nxt_mbuf_new_blank_wa(ya_allocator_get_default(), buffSize);
}

nxt_mbuf_t* nxt_mbuf_new_by_copy(const uint8_t *data, uint32_t dataLen)
{
    return nxt_mbuf_new_by_copy_wa(ya_allocator_get_default(), data, dataLen);
}

nxt_mbuf_t* nxt_mbuf_new_by_ref(const uint8_t *data, uint32_t dataLen)
{
    return nxt_mbuf_new_by_ref_wa(ya_allocator_get_default(), data, dataLen);
}

nxt_mbuf_t* nxt_mbuf_new_clone(nxt_mbuf_t *mbuf)
{
    return nxt_mbuf_new_clone_wa(ya_allocator_get_default(), mbuf);
}

void nxt_mbuf_free(nxt_mbuf_t *mbuf)
{
    return nxt_mbuf_free_wa(ya_allocator_get_default(), mbuf);
}
