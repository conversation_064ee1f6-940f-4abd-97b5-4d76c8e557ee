#ifndef PROTOMESSAGE_H
#define PROTOMESSAGE_H

#include "yaEngineNext/nxt_engine.h"
#include "mbuf.h"
#include "session.h"
#include "util/noncopyable.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/object_pool.h>
#include <string>

// pmessage 既能提供 precord, 又能提供 raw mbuf;
// pmessage 不拥有 mbuf, 但拥有 precord;
typedef struct nxt_ProtoMessage : public nxt::noncopyable
{
public:
    nxt_ProtoMessage(nxt_mbuf_t *mbuf, precord_t *precord, nxt_session_t *session)
        : mbuf_(mbuf)
        , precord_(precord)
        , session_(session)
    {
    }

    ~nxt_ProtoMessage()
    {
    }

public:
    const char* getMessageText()
    {
        return "";
    }

    nxt_mbuf_t *getMbuf()
    {
        return mbuf_;
    }

    precord_t *getPrecord()
    {
        return precord_;
    }

    nxt_session_t *getSession()
    {
        return session_;
    }

private:

private:
    nxt_mbuf_t        *mbuf_         = NULL;
    precord_t         *precord_      = NULL;
    nxt_session_t     *session_      = NULL;
} nxt_pmessage_t;

#endif /* PROTOMESSAGE_H */
