#include "recognizer.h"
#include "yaEngineNext/nxt_recognizer.h"
#include "dissector.h"
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/memory.hpp>
#include <vector>
#include <hs/hs.h>

enum
{
    NXT_REGEX_FLAGS_DEFAULT = HS_FLAG_SINGLEMATCH | HS_FLAG_DOTALL | HS_FLAG_MULTILINE | HS_FLAG_UTF8,
};

class nxt_RegexBlock
{
public:
    ~nxt_RegexBlock()
    {
        if (db_)
        {
            hs_free_database(db_);
        }

        if (scrach_)
        {
            hs_free_scratch(scrach_);
        }
    }

public:
    int appendPattern(const char *pattern, uint32_t id)
    {
        patternArray_.push_back(pattern);
        idArray_.push_back(id);
        flagArray_.push_back(NXT_REGEX_FLAGS_DEFAULT);

        return 0;
    }

    int compileRegexBlock()
    {
        if (db_ != NULL)
        {
            return 0;
        }

        if (patternArray_.empty())
        {
            return -1;
        }

        hs_error_t           err         = 0;
        hs_compile_error_t  *compileErr  = NULL;

        err = hs_compile_multi(patternArray_.data(), flagArray_.data(), idArray_.data(),
                               patternArray_.size(), HS_MODE_BLOCK, NULL, &db_, &compileErr);
        // 是否编译出错
        if (err != HS_SUCCESS)
        {
            if (compileErr->expression < 0)
            {
                printf("hs compile ERROR: %s\n", compileErr->message);
            }
            else
            {
                printf("hs compile ERROR: pattern [%s] error: %s\n",
                       patternArray_[compileErr->expression],
                       compileErr->message);
            }

            hs_free_compile_error(compileErr);
            return -1;
        }

        // pattern 编译成功, 分配 scratch 空间用于匹配;
        err = hs_alloc_scratch(db_, &scrach_);
        if (err != HS_SUCCESS)
        {
            hs_free_database(db_);
            return -1;
        }

        return 0;
    }

    static int onHsMatchEvent(unsigned int id,
                              unsigned long long from _U_, unsigned long long to _U_, unsigned int flags _U_,
                              void *userdata)
    {
        int *hitId = (int*)userdata;
        *hitId     = id;
        return 0;
    }

    uint32_t match(const uint8_t *inputData, uint32_t inputLen)
    {
        if (NULL == db_ || NULL == scrach_)
        {
            return 0;
        }

        // TODO: 多命中情形的处理;
        hitId_ = 0;
        hs_error_t err = hs_scan(db_, (const char *)inputData, inputLen, 0, scrach_, onHsMatchEvent, &this->hitId_);
        if (err != HS_SUCCESS)
        {
            return 0;
        }

        return hitId_;
    }

private:
    uint32_t       hitId_  = 0;
    hs_database_t *db_     = NULL;
    hs_scratch_t  *scrach_ = NULL;

private:
    std::vector<const char *>      patternArray_;
    std::vector<uint32_t>          idArray_;
    std::vector<uint32_t>          flagArray_;
};

nxt_Recognizer::nxt_Recognizer()
    : type_(NXT_RECOG_TYPE_PORT_AND_PAYLOAD)
{
    regexBlock_ = new nxt_RegexBlock;
}

nxt_Recognizer::nxt_Recognizer(nxt_Dissector *directorProto)
    : type_(NXT_RECOG_TYPE_PORT_ONLY)
    , directProto_(directorProto)
{
}

nxt_Recognizer::~nxt_Recognizer()
{
    delete regexBlock_;
}

int nxt_Recognizer::registerPattern(const char *pattern, nxt_Dissector *dissector)
{
    return regexBlock_->appendPattern(pattern, pointerToNumber(dissector));
}

int nxt_Recognizer::registerDone()
{
    if (type_ == NXT_RECOG_TYPE_PORT_AND_PAYLOAD)
    {
        return regexBlock_->compileRegexBlock();
    }

    return 0;
}

nxt_Dissector* nxt_Recognizer::recognizeProto(const uint8_t *payload, uint16_t payloadLen)
{
    uint32_t hitProtoId = regexBlock_->match(payload, payloadLen);
    if (0 == hitProtoId)
    {
        return NULL;
    }

    return static_cast<nxt_Dissector*>(pointerFromNumber(hitProtoId));
}

int nxt_ProtoRecognizer::registerPattern(uint16_t port, const char *pattern, nxt_Dissector *dissector)
{
    // 无 port 限定，添加到二级表;
    if (0 == port)
    {
        return tableLevel2_.registerPattern(pattern, dissector);
    }

    // 扩容 tableLevel1
    if (port > tableLevel1_.size())
    {
        tableLevel1_.resize(port + 1);
    }

    // 无 payload 限定
    if (NULL == pattern)
    {
        if (tableLevel1_[port] != nullptr)
        {
            // ERROR: port 对应的 recognizer 已经存在，
            // 不论已经存在的是 port 还是 payload 类型，都不能再注册 only-port 类型的 pattern;
            return -1;
        }

        // 创建 port-only 类型的 recognizer;
        tableLevel1_[port] = yv::make_unique<nxt_Recognizer>(dissector);
        return 0;
    }

    // port-and-payload
    if (tableLevel1_[port] == nullptr)
    {
        tableLevel1_[port] = yv::make_unique<nxt_Recognizer>();
    }

    return tableLevel1_[port]->registerPattern(pattern, dissector);
}

int nxt_ProtoRecognizer::registerDone()
{
    for (auto &p : tableLevel1_)
    {
        if (p)
        {
            p->registerDone();
        }
    }

    tableLevel2_.registerDone();
    return 0;
}

nxt_Dissector* nxt_ProtoRecognizer::recognizeProto(uint16_t port, uint8_t *payload, uint16_t payloadLen)
{
    if (port >= tableLevel1_.size())
    {
        return tableLevel2_.recognizeProto(payload, payloadLen);
    }

    auto &recog = tableLevel1_[port];

    // 一级表中不存在该端口的表项，到二级表中进行识别
    if (recog == nullptr)
    {
        return tableLevel2_.recognizeProto(payload, payloadLen);
    }

    // 一级表中该端口是一个 port-only 类型，直接返回 proto;
    if (recog->getRecogType() == nxt_Recognizer::NXT_RECOG_TYPE_PORT_ONLY)
    {
        return recog->getDirectProto();
    }

    // 执行 payload pattern matching;
    nxt_dissector_t *proto = recog->recognizeProto(payload, payloadLen);
    if (NULL != proto)
    {
        return proto;
    }

    // 一级表没有匹配到，二级表继续查找;
    return tableLevel2_.recognizeProto(payload, payloadLen);
}

typedef nxt_ProtoRecognizer nxt_recognizer_t;

nxt_recognizer_t* nxt_recog_create()
{
    return new nxt_ProtoRecognizer;
}

void nxt_recog_destroy(nxt_recognizer_t* recog)
{
    delete recog;
}

int nxt_recog_register(nxt_recognizer_t* recog, uint16_t port, const char *pattern, nxt_dissector_t *dissector)
{
    return recog->registerPattern(port, pattern, dissector);
}

nxt_dissector_t* nxt_recog_do_recog(nxt_recognizer_t* recog, uint16_t port, uint8_t *payload, uint16_t payloadLen)
{
    return recog->recognizeProto(port, payload, payloadLen);
}

int nxt_recog_register_done(nxt_recognizer_t* recog)
{
    return recog->registerDone();
}
