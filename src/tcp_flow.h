#ifndef TCPFLOW_H
#define TCPFLOW_H

#include "mbuf.h"
#include "engine_widget.hpp"
#include "util/noncopyable.h"
#include <assert.h>

/*
 * seq 大小比较
 */
#define SEQ_LT(a,b)  ((int)((a)-(b)) < 0)
#define SEQ_LEQ(a,b) ((int)((a)-(b)) <= 0)
#define SEQ_GT(a,b)  ((int)((a)-(b)) > 0)
#define SEQ_GEQ(a,b) ((int)((a)-(b)) >= 0)

class nxt_TcpSegmentNode : nxt::noncopyable
{
public:
nxt_TcpSegmentNode()
        : next_(this)
        , prev_(this)
    {
    }

nxt_TcpSegmentNode(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t len)
    : mbuf_(mbuf)
    , seq_(seq)
    , len_(len)
    {

    }

public:
    nxt_mbuf_t* getMbuf()
    {
        return mbuf_;
    }

    uint32_t getSeq()
    {
        return seq_;
    }

    uint32_t getNextSeq()
    {
        return seq_ + len_;
    }

    uint16_t getLen()
    {
        return len_;
    }

    int linkNext(nxt_TcpSegmentNode *s)
    {
        assert(s->prev_ == NULL && s->next_ == NULL && "newly segment must be a alone node.");

        s->next_           = this->next_;
        this->next_        = s;
        this->next_->prev_ = s;
        s->prev_           = this;
        return 0;
    }

    int linkPrev(nxt_TcpSegmentNode *s)
    {
        assert(s->prev_ == NULL && s->next_ == NULL && "newly segment must be a alone node.");

        this->prev_->next_ = s;
        s->next_           = this;
        s->prev_           = this->prev_;
        this->prev_        = s;
        return 0;
    }

    int unlink()
    {
        this->prev_->next_ = this->next_;
        this->next_->prev_ = this->prev_;
        this->prev_        = this->next_ = NULL;
        return 0;
    }

    nxt_TcpSegmentNode* getNext()
    {
        return next_;
    }

    nxt_TcpSegmentNode* getPrev()
    {
        return prev_;
    }

    int trimHead(uint16_t len)
    {
        trimHead_ = len;
        seq_ += len;
        len_ -= len;
        mbuf_->rangeAdjust(len, 0);   // 头部缩减，需要将 begin 向右移动，增加;
        return 0;
    }

    int trimTail(uint16_t len)
    {
        trimTail_ = len;
        len_ -= len;
        mbuf_->rangeAdjust(0, -len);  // 尾部缩减，需要将 end 和左移动，减小;
        return 0;
    }

    void copyToSegment(nxt_tcp_segment_t *s)
    {
        s->mbuf             = mbuf_;
        s->len              = len_;
        s->seq              = seq_;
        s->tcpHeadOffset    = tcpHeadOffset_;
        s->tcpPayloadOffset = tcpPayloadOffset_;
        s->trimHead         = trimHead_;
        s->trimTail         = trimTail_;
    }

private:
    nxt_TcpSegmentNode *next_             = NULL;
    nxt_TcpSegmentNode *prev_             = NULL;
    nxt_mbuf_t         *mbuf_             = NULL;
    uint32_t            seq_              = 0;
    uint16_t            len_              = 0; // payloadLen
    uint16_t            tcpHeadOffset_    = 0;
    uint16_t            tcpPayloadOffset_ = 0;
    uint16_t            trimHead_         = 0;
    uint16_t            trimTail_         = 0;
};

class nxt_TcpSegmentQueue : public nxt::noncopyable
{
public:
    nxt_TcpSegmentQueue(nxt_session_t *session);

    ~nxt_TcpSegmentQueue();

public:
    bool empty() const
    {
        return head_->getNext() == head_;
    }

    int enqueue(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t len);

    int enqueue(nxt_TcpSegmentNode *segment);

    nxt_TcpSegmentNode* dequeue();

    nxt_TcpSegmentNode* peek();

    uint32_t getQueueLength() const
    {
        return length_;
    }

    uint32_t getQueueTotalBytes() const
    {
        return totalBytes_;
    }

    uint32_t getStartSeq() const
    {
        return head_->getNext()->getSeq();
    }

    void showQueue(const char *queueName);

    int read(uint8_t *buff, uint32_t buffLen)
    {
        nxt_AppendableRawbuf rawBuff(buff, buffLen);
        return readToAppendableBuf(rawBuff, buffLen);
    }

    int readToRingbuf(nxt_ringbuf_t *rbuf, uint32_t readLen)
    {
        nxt_AppendableRingbuf ringBuff(rbuf);
        return readToAppendableBuf(ringBuff, readLen);
    }

protected:
    nxt_TcpSegmentNode* createTcpSegmentNode(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t len);
    void                destroyTcpSegmentNode(nxt_TcpSegmentNode *node);

private:
    template<typename AppendableBuf>
    int readToAppendableBuf(AppendableBuf &buf, uint32_t readLen)
    {
        // 一直读取，直到: 要么读取够了长度为 read_len 的数据，或者 queue 已经没有数据可以读取了;
        while (buf.getWrittenBytes() < readLen && !this->empty())
        {
            nxt_mbuf_t    *mbuf   = this->peek()->getMbuf();
            if (mbuf->getLength() == 0)
            {   // 可能这是一个 sync 报文; 直接出队;
                destroyTcpSegmentNode(this->dequeue()); // MEM-dettach-and-destroy: segment 从 commitQueue_ 中移除之后 delete;
                continue;
            }

            int            readSize = std::min(mbuf->getLength(), readLen - buf.getWrittenBytes());
            const uint8_t *b         = mbuf->getBytes(0, readSize);

            buf.append(b, readSize);
            nxt_mbuf_range_adjust(mbuf, readSize, 0);

            totalBytes_ -= readSize;
            if (mbuf->getLength() == 0)
            {   // buff 数据读尽，出队并销毁
                destroyTcpSegmentNode(this->dequeue());        // MEM-dettach-and-destroy: segment 从 commitQueue_ 中移除之后 delete;
            }
        }

        return buf.getWrittenBytes();
    }

protected:
    nxt_TcpSegmentNode *head_       = NULL;
    nxt_session_t      *session_    = NULL;
    uint32_t            length_     = 0;
    uint32_t            totalBytes_ = 0;
};

class nxt_TcpReassQueue : public nxt_TcpSegmentQueue
{
public:
    nxt_TcpReassQueue(nxt_session_t *session)
        : nxt_TcpSegmentQueue(session)
    {
    }

    int enqueue(nxt_mbuf_t *mbuf, uint32_t seq, uint16_t payloadLen);
};

// tcp flags
#define TH_FIN  0x01
#define TH_SYN  0x02
#define TH_RST  0x04
#define TH_PUSH 0x08
#define TH_ACK  0x10
#define TH_URG  0x20

#define NXT_TCP_CHECK_FLAG_SET(flags, flag) ((flags & flag) == flag)

struct nxt_TcpHdr {
    uint16_t srcPort; /* source port */
    uint16_t dstPort; /* destination port */
    uint32_t seq;     /* sequence number */
    uint32_t ack;     /* acknowledgement number */
#if BYTE_ORDER == LITTLE_ENDIAN
    uint8_t  x2:4,    /* (unused) */
             off:4;   /* data offset */
#endif
#if BYTE_ORDER == BIG_ENDIAN
    uint8_t  off:4,   /* data offset */
             x2:4;    /* (unused) */
#endif
    uint8_t  flags;
    uint16_t win;     /* window */
    uint16_t sum;     /* checksum */
    uint16_t urp;     /* urgent pointer */
};

class nxt_TcpFlow : public nxt::noncopyable
{
public:
    enum nxt_tcp_state
    {
        NXT_TCP_NEWLY = 0,                  // 刚刚新创建的 tcp flow
        NXT_TCP_LISTEN,
        NXT_TCP_SYNC,
        NXT_TCP_ESTABLISHED_BUT_NO_SYNC,    // 连接建立，但是没有遇到 sync;
        NXT_TCP_ESTABLISHED,                // 连接正常建立
        NXT_TCP_FIN,                        // finish;
        NXT_TCP_TIMEOUT,                    // 超时;
    };

public:
    nxt_TcpFlow(const nxt_tuple_5_ipv4_t &t5, nxt_session_t *session)
        : t5_(t5)
        , session_(session)
        , commitQueue_(session)
        , reassQueue_(session)
    {
    }

    int processPacket(nxt_engine_t *engine, nxt_mbuf_t *mbuf);

    uint32_t getAvailBytesLen()
    {
        return commitQueue_.getQueueTotalBytes();
    }

    int read(uint8_t *buff, uint32_t buffLen, nxt_stream_read_res_t *readStatus);

    int readToRingbuf(nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus);

public:
    const nxt_TcpSegmentQueue* getCommitQueue()
    {
        return &commitQueue_;
    }

    const nxt_TcpSegmentQueue* getReassQueue()
    {
        return &reassQueue_;
    }

    nxt_session_t* getSession()
    {
        return session_;
    }

private:
    int enqueueReass(nxt_engine_t *engine, nxt_mbuf_t *mbuf, uint32_t seq, uint16_t headerLen, uint16_t payloadLen);
    int checkReassedToCommit(nxt_engine_t *engine);

private:
    nxt_tuple_5_ipv4_t   t5_;
    nxt_session_t       *session_       = NULL;
    nxt_tcp_state        state_         = NXT_TCP_NEWLY;
    uint32_t             nextExpectSeq_ = 0;
    nxt_TcpSegmentQueue  commitQueue_;
    nxt_TcpReassQueue    reassQueue_;
};

#endif /* TCPFLOW_H */
