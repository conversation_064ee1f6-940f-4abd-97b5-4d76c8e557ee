#include <yaEngineNext/nxt_parser_core.h>

%%{
    machine fsm;
    alphtype unsigned char;
    include basic "nxt_parser_basic.rl";

    action done               { fbreak; }
    action method             { OUT_AS("Method"); }
    action uri                { OUT_AS("URI"); }
    action version            { OUT_AS("Version"); }
    action status             { OUT_AS("Status"); }

    CRLF = ( "\r\n" | "\n" ) ;
    SP = 0x20;
    CR = 0x0D;
    LF = 0x0A;
    HT = 0x09;
    LWS = (CRLF)? (SP | HT){1,};
    SWS = (LWS)?;
    HCOLON = (SP | HT)* ":" SWS;
    HTTP_CTL = (0 - 31) | 127 ;

    # URI
    URI = (ascii -- (HTTP_CTL | SP))* ;

    # Method
    Method = "OPTIONS"
            | "GET"
            | "HEAD"
            | "POST"
            | "PUT"
            | "DELETE"
            | "TRACE"
            | "CONNECT" ;

    http_number = ( "1." ("0" | "1") ) ;
    HTTP_Version = ( "HTTP/" http_number ) ;
    Request_Line = ( Method >mark %method " " URI >mark %uri " " HTTP_Version >mark %version CRLF ) ;

    Reason_Phrase = (any -- CRLF)+;
    Status_Code = digit+;
    Status_Line = HTTP_Version >mark %version " " Status_Code >mark %status " " Reason_Phrase CRLF;

    HTTP_separator = ( "(" | ")" | "<" | ">" | "@"
                     | "," | ";" | ":" | "\\" | "\""
                     | "/" | "[" | "]" | "?" | "="
                     | "{" | "}" | " " | "\t"
                     ) ;
    lws = CRLF? (" " | "\t")+ ;
    token = ascii -- ( HTTP_CTL | HTTP_separator ) ;
    content = ((any -- HTTP_CTL) | lws);
    field_name = ( token )+ ;
    field_value = content* ;

    # HTTP header parsing
    # general header
    general_header = field_name HCOLON field_value ;
    message_header = ( "Host" HCOLON field_value              >mark %{ OUT_AS("Host"); }
                     | "User-Agent" HCOLON field_value        >mark %{ OUT_AS("User-Agent"); }
                     | "Cookie" HCOLON field_value            >mark %{ OUT_AS("Cookie"); }
                     | "Content-Length" HCOLON field_value    >mark %{ OUT_AS("Content-Length"); }
                     | "Transfer-Encoding" HCOLON field_value >mark %{ OUT_AS("Transfer-Encoding"); }
                     | "Date" HCOLON field_value              >mark %{ OUT_AS("Date"); }
                     | "Accept" HCOLON field_value            >mark %{ OUT_AS("Accept"); }
                     | "Accept-Charset"  HCOLON field_value   >mark %{ OUT_AS("Accept-Charset"); }
                     | "Accept-Encoding" HCOLON field_value   >mark %{ OUT_AS("Accept-Encoding"); }
                     | "Accept-Language" HCOLON field_value   >mark %{ OUT_AS("Accept-Language"); }
                     | general_header
                     ) :> CRLF;

    Message = (Request_Line | Status_Line) message_header* ( CRLF ) ;
    main := Message @done;
}%%

%% write data;

int nxt_parser_http_init(nxt_parser_t *parser)
{
    int cs = 0;
    %% write init;

    parser->ragelCS = cs;  // 初始化起始状态;
    return 0;
}

int nxt_parser_http_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata)
{
    // 准备 ragel fsm 需要的变量:cs, p, pe, eof
    int cs             = parser->ragelCS;   // 从 parser 恢复 cs;
    const uint8_t *p   = buff;
    const uint8_t *pe  = buff + len;
    const uint8_t *eof = pe;

    // 执行 parse
    %% write exec;

    // parse 结束，更新状态到 parser 中;
    parser->ragelPoint  = p;
    parser->ragelCS = cs;                   // 将 cs 保存到 parser;

    // 检查解析结果
    if (cs == fsm_error)
    {
      parser->status = NXT_PSTATUS_ERROR;
      return -1;
    }

    if (cs >= fsm_first_final)
    {
      parser->status = NXT_PSTATUS_COMPLETE;
    }
    else
    {
      parser->status = NXT_PSTATUS_PARTIAL;
    }

    // 检测是否需要执行回退操作(例如在末尾时发现一个 token 被 mark 但不没有结束, 发生了截断);
    nxt_parser_check_rollback(parser, p, eof);

    // 计算消耗了多少字节;
    return parser->ragelPoint - buff;
}
