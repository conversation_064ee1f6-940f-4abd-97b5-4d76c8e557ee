# cmake version
cmake_minimum_required(VERSION 3.14)
set(YA_PROJECT_LIBRARY parser)

include(${CMAKE_SOURCE_DIR}/cmake/yaEngineNextConfig.cmake)

#
# ragel parser
# 生成的对应 .c 文件构成一个列表，存储在变量 ragel_output_list 中
add_ragel_parser_list(
  parser_rtsp.rl
  parser_http.rl
)

add_library(${YA_PROJECT_LIBRARY} OBJECT
  parser_core.c
  ${ragel_output_list}
)

target_include_directories(${YA_PROJECT_LIBRARY} PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}) # 当前项目的 SOURCE_DIR, 指 parsers 目录
target_include_directories(${YA_PROJECT_LIBRARY} PRIVATE ${CMAKE_SOURCE_DIR}/include) # 顶层项目下的 include 目录，指 libyaEngineNext/include 目录
set_target_properties(${YA_PROJECT_LIBRARY} PROPERTIES POSITION_INDEPENDENT_CODE ON)
