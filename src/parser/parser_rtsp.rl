#include <yaEngineNext/nxt_parser_core.h>

%%{
    machine fsm;
    alphtype unsigned char;
    include basic "nxt_parser_basic.rl";

    action method             { OUT_AS("method"); }
    action version            { OUT_AS("version"); }
    action uri                { OUT_AS("uri"); }
    action status             { OUT_AS("status"); }
    action reason_phrase      { OUT_AS("reason_phrase"); }
    action accept_encoding    { OUT_AS("accept_encoding"); }
    action accept_language    { OUT_AS("accept_language"); }
    action allow              { OUT_AS("allow"); }
    action authorization      { OUT_AS("authorization"); }
    action bandwidth          { OUT_AS("bandwidth"); }
    action blocksize          { OUT_AS("blocksize"); }
    action cache_control      { OUT_AS("cache_control"); }
    action content_base       { OUT_AS("content_base"); }
    action content_encoding   { OUT_AS("content_encoding"); }
    action content_language   { OUT_AS("content_language"); }
    action content_length     { OUT_AS("content_length"); }
    action content_location   { OUT_AS("content_location"); }
    action content_type       { OUT_AS("content_type"); }
    action cseq               { OUT_AS("cseq"); }
    action date               { OUT_AS("date"); }
    action expires            { OUT_AS("expires"); }
    action from_              { OUT_AS("from"); } # from 对于 ragel 是一个保留字，调整为 from_;
    action if_match           { OUT_AS("if_match"); }
    action if_modified_since  { OUT_AS("if_modified_since"); }
    action last_modified      { OUT_AS("last_modified"); }
    action location           { OUT_AS("location"); }
    action proxy_authenticate { OUT_AS("proxy_authenticate"); }
    action proxy_require      { OUT_AS("proxy_require"); }
    action public             { OUT_AS("public"); }
    action range              { OUT_AS("range"); }
    action retry_after        { OUT_AS("retry_after"); }
    action require            { OUT_AS("require"); }
    action rtp_info           { OUT_AS("rtp_info"); }
    action scale              { OUT_AS("scale"); }
    action speed              { OUT_AS("speed"); }
    action server             { OUT_AS("server"); }
    action session            { OUT_AS("session"); }
    action supported          { OUT_AS("supported"); }
    action timestamp          { OUT_AS("timestamp"); }
    action transport          { OUT_AS("transport"); }
    action unsupported        { OUT_AS("unsupported"); }
    action user_agent         { OUT_AS("user_agent"); }
    action via                { OUT_AS("via"); }
    action www_authenticate   { OUT_AS("www_authenticate"); }

    token = (0x21 | 0x23..0x27 | 0x2A..0x2B | 0x2D..0x2E | 0x30..0x39 | 0x41..0x5A | 0x5E..0x7A | 0x7C | 0x7E){1,};
    extension_method = token;
    Method = ("DESCRIBE" | "GET_PARAMETER" | "OPTIONS" | "PAUSE" | "PLAY" | "PLAY_NOTIFY"
              | "REDIRECT" | "SETUP" | "SET_PARAMETER" | "TEARDOWN" | extension_method) >mark %method;
    SP = 0x20;
    Request_URI = (any - (0x20))+ >mark %uri; # rewritten rule, old define is: Request_URI = "*" | RTSP_REQ_URI;
    DIGIT = 0x30..0x39;
    RTSP_Version = ("RTSP/" DIGIT{1,} "." DIGIT{1,}) >mark %version;
    CR = 0x0D;
    LF = 0x0A;
    CRLF = CR LF;
    Request_Line = Method SP Request_URI SP RTSP_Version CRLF;
    HT = 0x09;
    LWS = (CRLF)? (SP | HT){1,};
    SWS = (LWS)?;
    HCOLON = (SP | HT)* ":" SWS;
    extension_format = token;
    range_unit = "npt" | "smpte" | "smpte-30-drop" | "smpte-25" | "clock" | extension_format;
    COMMA = SWS "," SWS;
    acceptable_ranges = (range_unit (COMMA range_unit)*);
    Accept_Ranges = "Accept-Ranges" HCOLON acceptable_ranges;
    EQUAL = SWS "=" SWS;
    delta_seconds = DIGIT{1,19};
    DQUOTE = 0x22;
    quoted_pair = "\\\\" | ("\\" DQUOTE);
    UTF8_2 = (any - (0x0d|0x22))+; # rewritten rule, old define is: UTF8_2 = <As defined in RFC 3629>;
    UTF8_3 = (any - (0x0d|0x22))+; # rewritten rule, old define is: UTF8_3 = <As defined in RFC 3629>;
    UTF8_4 = (any - (0x0d|0x22))+; # rewritten rule, old define is: UTF8_4 = <As defined in RFC 3629>;
    UTF8_NONASCII = UTF8_2 | UTF8_3 | UTF8_4;
    qdtext = 0x20..0x21 | 0x23..0x5B | 0x5D..0x7E | quoted_pair | UTF8_NONASCII;
    quoted_string = (DQUOTE qdtext* DQUOTE);
    cache_extension = token (EQUAL (token | quoted_string))?;
    cache_rqst_directive = "no-cache" | "max-stale" (EQUAL delta_seconds)? | "min-fresh" EQUAL delta_seconds | "only-if-cached" | cache_extension;
    cache_rspns_directive = "public" | "private" | "no-cache" | "no-transform" | "must-revalidate" | "proxy-revalidate" | "max-age" EQUAL delta_seconds | cache_extension;
    cache_directive = cache_rqst_directive | cache_rspns_directive;
    Cache_Control = "Cache-Control" HCOLON (cache_directive (COMMA cache_directive)*) >mark %cache_control;
    connection_token = "close" | token;
    Connection = "Connection" HCOLON connection_token (COMMA connection_token)*;
    cseq_nr = DIGIT{1,9};
    CSeq = "CSeq" HCOLON cseq_nr >mark %cseq;
    date_time = (any - (0x0d))+; # rewritten rule, old define is: date_time = <As defined in RFC 5322>;
    RTSP_date = date_time;
    Date = "Date" HCOLON RTSP_date >mark %date;
    POS_FLOAT = DIGIT{1,12} ("." DIGIT{1,9})?;
    utc_date = DIGIT{8};
    utc_clock = DIGIT{6} ("." DIGIT{1,9})?;
    utc_time = utc_date "T" utc_clock "Z";
    FLOAT = ("-")? POS_FLOAT;
    scale_value = FLOAT;
    COLON = SWS ":" SWS;
    scale_entry = scale_value | (scale_value COLON scale_value);
    scale_value_list = DQUOTE scale_entry (COMMA scale_entry)* DQUOTE;
    ALPHA = 0x41..0x5A | 0x61..0x7A;
    safe = "$" | "-" | "_" | "." | "+";
    rtsp_extra = "!" | "*" | "'" | "(" | ")";
    rtsp_unreserved = ALPHA | DIGIT | safe | rtsp_extra;
    media_prop_ext = token (EQUAL (rtsp_unreserved{1,} | quoted_string))?;
    media_prop_value = ("Random-Access" (EQUAL POS_FLOAT)?) | "Beginning-Only" | "No-Seeking" | "Immutable" | "Dynamic" | "Time-Progressing" | "Unlimited" | ("Time-Limited" EQUAL utc_time) | ("Time-Duration" EQUAL POS_FLOAT) | ("Scales" EQUAL scale_value_list) | media_prop_ext;
    media_prop_list = media_prop_value (COMMA media_prop_value)*;
    Media_Properties = "Media-Properties" HCOLON (media_prop_list)?;
    npt_range_spec = (any - (0x20|0x0d|","|0x09))+; # rewritten rule, old define is: npt_range_spec = (npt_time "-" (npt_time)?) | ("-" npt_time);
    npt_range = "npt" (EQUAL npt_range_spec)?;
    utc_range_spec = (utc_time "-" (utc_time)?) | ("-" utc_time);
    utc_range = "clock" (EQUAL utc_range_spec)?;
    smpte_type_extension = "smpte" token;
    smpte_type = "smpte" | "smpte-30-drop" | "smpte-25" | smpte_type_extension;
    smpte_time = DIGIT{1,2} ":" DIGIT{1,2} ":" DIGIT{1,2} (":" DIGIT{1,2} ("." DIGIT{1,2})?)?;
    smpte_range_spec = (smpte_time "-" (smpte_time)?) | ("-" smpte_time);
    smpte_range = smpte_type (EQUAL smpte_range_spec)?;
    range_value = (rtsp_unreserved | quoted_string | ":"){1,};
    range_ext = extension_format (EQUAL range_value)?;
    ranges_spec = npt_range | utc_range | smpte_range | range_ext;
    ranges_list = ranges_spec (COMMA ranges_spec)*;
    Media_Range = "Media-Range" HCOLON (ranges_list)?;
    startup_id = DIGIT{1,8};
    Pipelined_Requests = "Pipelined-Requests" HCOLON startup_id;
    feature_tag = token;
    feature_tag_list = feature_tag (COMMA feature_tag)*;
    Proxy_Supported = "Proxy-Supported" HCOLON (feature_tag_list)?;
    Range = "Range" HCOLON ranges_spec >mark %range;
    RTSP_REQ_Ref = (any - (0x0d|0x22))+; # rewritten rule, old define is: RTSP_REQ_Ref = RTSP_REQ_URI | RTSP_REQ_Rel;
    stream_url = "url" EQUAL DQUOTE RTSP_REQ_Ref DQUOTE;
    HEX = DIGIT | "A" | "B" | "C" | "D" | "E" | "F" | "a" | "b" | "c" | "d" | "e" | "f";
    ssrc = HEX{8};
    host = (any - (0x20|0x0d|0x22|";"|","|":"|0x09))+; # rewritten rule, old define is: host = < As defined in RFC 3986>;
    gen_value = token | host | quoted_string;
    generic_param = token (EQUAL gen_value)?;
    ri_parameter = ("seq" EQUAL DIGIT{1,5}) | ("rtptime" EQUAL DIGIT{1,10}) | generic_param;
    SEMI = SWS ";" SWS;
    ssrc_parameter = LWS "ssrc" EQUAL ssrc HCOLON ri_parameter (SEMI ri_parameter)*;
    rtsp_info_spec = stream_url ssrc_parameter{1,};
    RTP_Info = "RTP-Info" HCOLON (rtsp_info_spec (COMMA rtsp_info_spec)*)? >mark %rtp_info;
    Scale = "Scale" HCOLON scale_value >mark %scale;
    Seek_S_value_ext = token;
    Seek_S_values = "RAP" | "CoRAP" | "First-Prior" | "Next" | Seek_S_value_ext;
    Seek_Style = "Seek-Style" HCOLON Seek_S_values;
    SLASH = SWS "/" SWS;
    product_version = token;
    product = token (SLASH product_version)?;
    LPAREN = SWS "(" SWS;
    ctext = 0x20..0x27 | 0x2A..0x7E | 0x80..0xFF;
    RPAREN = SWS ")" SWS;
    comment = LPAREN (ctext | quoted_pair)* RPAREN;
    Server = "Server" HCOLON ((product | comment) (LWS (product | comment))*) >mark %server;
    session_id = (ALPHA | DIGIT | safe){1,256};
    Session = "Session" HCOLON (session_id (SEMI "timeout" EQUAL delta_seconds)?) >mark %session;
    lower_bound = POS_FLOAT;
    MINUS = SWS "-" SWS;
    upper_bound = POS_FLOAT;
    Speed = "Speed" HCOLON (lower_bound MINUS upper_bound) >mark %speed;
    Supported = "Supported" HCOLON (feature_tag_list)? >mark %supported;
    timestamp_value = DIGIT{,19} ("." DIGIT{,9})?;
    delay = DIGIT{,9} ("." DIGIT{,9})?;
    Timestamp = "Timestamp" HCOLON (timestamp_value (LWS delay)?) >mark %timestamp;
    profile = "AVP" | "SAVP" | "AVPF" | "SAVPF" | token;
    lower_transport = "TCP" | "UDP" | token;
    trans_id_rtp = "RTP/" profile ("/" lower_transport)?;
    other_trans = token ("/" token)*;
    transport_id = trans_id_rtp | other_trans;
    channel = DIGIT{1,3};
    ttl = DIGIT{1,3};
    mode = "PLAY" | token;
    mode_spec = (DQUOTE mode (COMMA mode)* DQUOTE);
    port = (any - (0x20|0x0d|0x22|";"|0x09))+; # rewritten rule, old define is: port = < As defined in RFC 3986>;
    host_port = (host (":" port)?) | (":" port);
    extension_addr = qdtext{1,};
    quoted_addr = DQUOTE (host_port | extension_addr) DQUOTE;
    addr_list = quoted_addr (SLASH quoted_addr)*;
    contrans_setup = "active" | "passive" | "actpass";
    contrans_con = "new" | "existing";
    base64_char = ALPHA | DIGIT | "+" | "/";
    base64_unit = base64_char{4};
    base64_pad = (base64_char{2} "==") | (base64_char{3} "=");
    base64 = base64_unit* (base64_pad)?;
    MIKEY_Value = base64;
    par_name = token;
    trn_par_value = (rtsp_unreserved | quoted_string)*;
    trn_param_ext = par_name (EQUAL trn_par_value)?;
    trns_parameter = (SEMI ("unicast" | "multicast")) | (SEMI "interleaved" EQUAL channel ("-" channel)?) | (SEMI "ttl" EQUAL ttl) | (SEMI "layers" EQUAL DIGIT{1,}) | (SEMI "ssrc" EQUAL ssrc(SLASH ssrc)*) | (SEMI "mode" EQUAL mode_spec) | (SEMI "dest_addr" EQUAL addr_list) | (SEMI "src_addr" EQUAL addr_list) | (SEMI "setup" EQUAL contrans_setup) | (SEMI "connection" EQUAL contrans_con) | (SEMI "RTCP-mux") | (SEMI "MIKEY" EQUAL MIKEY_Value) | (SEMI trn_param_ext);
    transport_spec = transport_id trns_parameter*;
    Transport = "Transport" HCOLON (transport_spec (COMMA transport_spec)*) >mark %transport;
    User_Agent = "User-Agent" HCOLON ((product | comment) (LWS (product | comment))*) >mark %user_agent;
    protocol_name = "RTSP" | token;
    protocol_version = token;
    other_transport = token;
    transport_prot = "UDP" | "TCP" | "TLS" | other_transport;
    sent_protocol = protocol_name SLASH protocol_version SLASH transport_prot;
    sent_by = host (COLON port)?;
    via_ttl = "ttl" EQUAL ttl;
    via_maddr = "maddr" EQUAL host;
    IPv4address = (any - (0x20|0x0d|","|0x09))+; # rewritten rule, old define is: IPv4address = < As defined in RFC 3986>;
    IPv6address = (any - (0x20|0x0d|","|0x09))+; # rewritten rule, old define is: IPv6address = < As defined in RFC 3986>;
    via_received = "received" EQUAL (IPv4address | IPv6address);
    via_extension = generic_param;
    via_params = via_ttl | via_maddr | via_received | via_extension;
    via_parm = sent_protocol LWS sent_by (SEMI via_params)*;
    Via = "Via" HCOLON (via_parm (COMMA via_parm)*) >mark %via;
    header_name = token;
    TEXT_UTF8char = 0x21..0x7E | UTF8_NONASCII;
    header_value = (TEXT_UTF8char | LWS)*;
    extension_header = header_name HCOLON header_value;
    general_header = Accept_Ranges | Cache_Control | Connection | CSeq | Date | Media_Properties | Media_Range | Pipelined_Requests | Proxy_Supported | Range | RTP_Info | Scale | Seek_Style | Server | Session | Speed | Supported | Timestamp | Transport | User_Agent | Via | extension_header;
    ietf_token = token;
    x_token = "x-" token;
    extension_token = ietf_token | x_token;
    discrete_type = "text" | "image" | "audio" | "video" | "application" | extension_token;
    composite_type = "message" | "multipart" | extension_token;
    m_type = discrete_type | composite_type;
    iana_token = token;
    m_subtype = extension_token | iana_token;
    m_attribute = token;
    m_value = token | quoted_string;
    m_parameter = m_attribute EQUAL m_value;
    media_type_range = ("*/*" | (m_type SLASH "*") | (m_type SLASH m_subtype)) (SEMI m_parameter)*;
    qvalue = ("0" ("." DIGIT{,3})?) | ("1" ("." ("0"){,3})?);
    accept_params = "q" EQUAL qvalue (SEMI generic_param)*;
    accept_range = media_type_range (SEMI accept_params)?;
    Accept = "Accept" HCOLON (accept_range (COMMA accept_range)*)?;
    cred_decision = (any - (0x0d))+; # rewritten rule, old define is: cred_decision = ("User" (LWS cred_info)?) | "Proxy" | "Any" | (token (LWS header_value{1,})?);
    Accept_Credentials = "Accept-Credentials" HCOLON cred_decision;
    content_coding = "identity" | token;
    codings = content_coding | "*";
    encoding = codings (SEMI accept_params)?;
    Accept_Encoding = "Accept-Encoding" HCOLON (encoding (COMMA encoding)*)? >mark %accept_encoding;
    primary_tag = ALPHA{1,8};
    subtag = ALPHA{1,8};
    language_tag = primary_tag ("-" subtag)*;
    language_range = language_tag | "*";
    language = language_range (SEMI accept_params)?;
    Accept_Language = "Accept-Language" HCOLON (language (COMMA language)*) >mark %accept_language;
    credentials = (any - (0x0d))+; # rewritten rule, old define is: credentials = <As defined by RFC 7235>;
    Authorization = "Authorization" HCOLON credentials >mark %authorization;
    Bandwidth = "Bandwidth" HCOLON DIGIT{1,19} >mark %bandwidth;
    Blocksize = "Blocksize" HCOLON DIGIT{1,9}  >mark %blocksize;
    name_addr = (any - (0x20|";"|0x09|0x0d))+; # rewritten rule, old define is: name_addr = (display_name)? LAQUOT addr_spec RAQUOT;
    RTSP_REQ_URI = (any - (0x20|0x0d|0x22|";"|">"|0x09))+; # rewritten rule, old define is: RTSP_REQ_URI = schemes ":" URI_req_rest;
    absolute_URI = (any - (0x20|0x0d|";"|0x09|">"))+; # rewritten rule, old define is: absolute_URI = < As defined in RFC 3986>;
    addr_spec = RTSP_REQ_URI | absolute_URI;
    tag_param = "tag" EQUAL token;
    from_param = tag_param | generic_param;
    from_spec = (name_addr | addr_spec) (SEMI from_param)*;
    From = "From" HCOLON from_spec >mark %from_;
    weak = "W/";
    opaque_tag = quoted_string;
    message_tag = (weak)? opaque_tag;
    message_tag_list = message_tag (COMMA message_tag)*;
    If_Match = "If-Match" HCOLON ("*" | message_tag_list) >mark %if_match;
    If_Modified_Since = "If-Modified-Since" HCOLON RTSP_date >mark %if_modified_since;
    If_None_Match = "If-None-Match" HCOLON ("*" | message_tag_list);
    Notify_Reason_extension = token;
    Notify_Reas_val = "end-of-stream" | "media-properties-update" | "scale-change" | Notify_Reason_extension;
    Notify_Reason = "Notify-Reason" HCOLON Notify_Reas_val;
    Proxy_Authorization = "Proxy-Authorization" HCOLON credentials;
    Proxy_Require = "Proxy-Require" HCOLON feature_tag_list >mark %proxy_require;
    RTSP_URI_Ref = (any - (0x0d))+; # rewritten rule, old define is: RTSP_URI_Ref = RTSP_URI | RTSP_Relative;
    Referrer = "Referrer" HCOLON (absolute_URI | RTSP_URI_Ref);
    cseq_info = "cseq" EQUAL cseq_nr;
    extension_code = DIGIT{3};
    Status_Code = ("100" | "200" | "301" | "302" | "303" | "304" | "305" | "400" | "401" | "402" | "403" | "404" | "405" | "406" | "407" | "408" | "410" | "412" | "413" | "414" | "415" | "451" | "452" | "453" | "454" | "455" | "456" | "457" | "458" | "459" | "460" | "461" | "462" | "463" | "464" | "465" | "466" | "470" | "471" | "472" | "500" | "501" | "502" | "503" | "504" | "505" | "551" | "553" | extension_code) >mark %status;
    status_info = "status" EQUAL Status_Code;
    Reason_Phrase = (TEXT_UTF8char | HT | SP){1,} >mark %reason_phrase;
    reason_info = "reason" EQUAL DQUOTE Reason_Phrase DQUOTE;
    req_status_info = cseq_info LWS status_info LWS reason_info;
    Request_Status = "Request-Status" HCOLON req_status_info;
    Require = "Require" HCOLON feature_tag_list >mark %require;
    TR_Reason = "Session-Timeout" | "Server-Admin" | "Internal-Error" | token;
    TR_time = "time" EQUAL utc_time;
    TR_user_msg = "user-msg" EQUAL quoted_string;
    TR_Parameter = TR_time | TR_user_msg | generic_param;
    TR_Info = TR_Reason (SEMI TR_Parameter)*;
    Terminate_Reason = "Terminate-Reason" HCOLON TR_Info;
    request_header = Accept | Accept_Credentials | Accept_Encoding | Accept_Language | Authorization | Bandwidth | Blocksize | From | If_Match | If_Modified_Since | If_None_Match | Notify_Reason | Proxy_Authorization | Proxy_Require | Referrer | Request_Status | Require | Terminate_Reason | extension_header;
    Allow = "Allow" HCOLON (Method (COMMA Method)*) >mark %allow;
    RTSP_URI = (any - (0x0d))+; # rewritten rule, old define is: RTSP_URI = schemes ":" URI_rest;
    Content_Base = "Content-Base" HCOLON RTSP_URI >mark %content_base;
    Content_Encoding = "Content-Encoding" HCOLON (content_coding (COMMA content_coding)*) >mark %content_encoding;
    Content_Language = "Content-Language" HCOLON (language_tag (COMMA language_tag)*) >mark %content_language;
    Content_Length = "Content-Length" HCOLON DIGIT{1,19} >mark %content_length;
    Content_Location = "Content-Location" HCOLON RTSP_REQ_Ref >mark %content_location;
    media_type = m_type SLASH m_subtype (SEMI m_parameter)*;
    Content_Type = "Content-Type" HCOLON media_type >mark %content_type;
    Expires = "Expires" HCOLON RTSP_date >mark %expires;
    Last_Modified = "Last-Modified" HCOLON RTSP_date >mark %last_modified;
    message_body_header = Allow | Content_Base | Content_Encoding | Content_Language | Content_Length | Content_Location | Content_Type | Expires | Last_Modified | extension_header;
    OCTET = 0x00..0xFF;
    message_body_data = OCTET{1,};
    Request = Request_Line ((general_header | request_header | message_body_header) CRLF)* CRLF (message_body_data)?;
    Status_Line = RTSP_Version SP Status_Code SP Reason_Phrase CRLF;
    auth_param_list = (any - (0x0d))+; # rewritten rule, old define is: auth_param_list = <As the Authentication-Info element in RFC 7615>;
    Authentication_Info = "Authentication-Info" HCOLON auth_param_list;
    cred_chain = DQUOTE RTSP_REQ_URI DQUOTE SEMI base64;
    Connection_Credentials = "Connection-Credentials" HCOLON cred_chain;
    Location = "Location" HCOLON RTSP_REQ_URI >mark %location;
    MTag = "MTag" HCOLON message_tag;
    challenge_list = (any - (0x0d))+; # rewritten rule, old define is: challenge_list = <As defined by the WWW-Authenticate in RFC 7235>;
    Proxy_Authenticate = "Proxy-Authenticate" HCOLON challenge_list >mark %proxy_authenticate;
    Proxy_Authentication_Info = "Proxy-Authentication-Info" HCOLON auth_param_list;
    Public = "Public" HCOLON (Method (COMMA Method)*) >mark %public;
    Retry_After = "Retry-After" HCOLON (RTSP_date | delta_seconds) >mark %retry_after;
    Unsupported = "Unsupported" HCOLON feature_tag_list >mark %unsupported;
    WWW_Authenticate = "WWW-Authenticate" HCOLON challenge_list >mark %www_authenticate;
    response_header = Authentication_Info | Connection_Credentials | Location | MTag | Proxy_Authenticate | Proxy_Authentication_Info | Public | Retry_After | Unsupported | WWW_Authenticate | extension_header;
    Response = Status_Line ((general_header | response_header | message_body_header) :> CRLF)* :> CRLF (message_body_data)?;
    RTSP_message = Request | Response;

    main := RTSP_message;
}%%

%% write data;

int nxt_parser_rtsp_init(nxt_parser_t *parser)
{
    int cs = 0;
    %% write init;

    parser->ragelCS = cs;  // 初始化起始状态;
    return 0;
}

int nxt_parser_rtsp_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata)
{
    // 准备 ragel fsm 需要的变量:cs, p, pe, eof
    int cs             = parser->ragelCS;   // 从 parser 恢复 cs;
    const uint8_t *p   = buff;
    const uint8_t *pe  = buff + len;
    const uint8_t *eof = pe;

    // 执行 parse
    %% write exec;

    // parse 结束，更新状态到 parser 中;
    parser->ragelPoint  = p;
    parser->ragelCS = cs;                   // 将 cs 保存到 parser;

    // 检查解析结果
    if (cs == fsm_error)
    {
      parser->status = NXT_PSTATUS_ERROR;
      return -1;
    }

    if (cs >= fsm_first_final)
    {
      parser->status = NXT_PSTATUS_COMPLETE;
    }
    else
    {
      parser->status = NXT_PSTATUS_PARTIAL;
    }

    // 检测是否需要执行回退操作(例如在末尾时发现一个 token 被 mark 但不没有结束, 发生了截断);
    nxt_parser_check_rollback(parser, p, eof);

    // 计算消耗了多少字节;
    return parser->ragelPoint - buff;
}
