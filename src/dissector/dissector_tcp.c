#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_recognizer.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "tcp"

static
int tcp_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    uint16_t srcport = nxt_mbuf_get_uint16_ntoh(mbuf, 0);
    uint16_t dstport = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    // put ports to engine regzone
    nxt_engine_pktzone_put_ports(engine, srcport, dstport);

    precord_put(precord, "srcport", uinteger, srcport);
    precord_put(precord, "dstport", uinteger, dstport);

    precord_put_to_layer(precord, "basic", "SrcPort", uinteger, srcport);
    precord_put_to_layer(precord, "basic", "DstPort", uinteger, dstport);

    // 当前 packet 方向设定
    // 方向设定需要有更可靠的方法，例如：sync 帧的 src 为 client
    nxt_direction_enum packetDirection = srcport < dstport ? NXT_DIR_S2C : NXT_DIR_C2S;
    nxt_engine_regzone_put_direction(engine, packetDirection);

    // 从 precord 中得到 packet 五元组，在 engine 的 sessionTable 中进行查找得到 session;
    // TODO: 此处应该由各个 layer dissector 各自存放到 engine register 中，这里从 register 获取即可;
    // 从 precord 中获取比较繁琐;
    nxt_tuple_5_ipv4_t t5;
    nxt_engine_pktzone_get_t5_ipv4(engine, &t5);

    nxt_session_t* sessionFind = nxt_engine_find_session(engine, &t5);
    if (NULL == sessionFind)
    {   // 注意: nxt_engine_new_session 需要传入一个 C2S 方向的 t5;
        sessionFind = nxt_engine_new_tcp_session(engine, &t5, mbuf, nxt_engine_regzone_get_dissector(engine), precord);
    }

    // 将 session 更新给 engine, 以便传递给 next dissector
    if (NULL == session)
    {
        session = sessionFind;
        nxt_engine_regzone_put_session(engine, sessionFind);
    }

    uint8_t headerLen = (nxt_mbuf_get_uint8(mbuf, 12) >> 4) * 4;
    if (nxt_mbuf_get_length(mbuf) < headerLen)
    {   // tcp headerLen 长度不足? 这是个错误，至少需要有 headerLen 那么长;
        // TODO: 报错，这是个异常 tcp packet, 需要丢弃;
        return nxt_mbuf_get_length(mbuf);
    }

    // handoff, set key; TODO: 需要让 handoff_set_key 成为最后一条语句，
    // 但是，nxt_session_process_packet 会修改 mbuf，需要调整;
    uint8_t* payload     = (uint8_t *)nxt_mbuf_get_raw(mbuf, headerLen);
    uint16_t payloadLen = nxt_mbuf_get_length(mbuf) - headerLen;
    nxt_handoff_set_key_of_port_payload(engine, srcport < dstport ? srcport : dstport, payload, payloadLen);

    // mbuf 可能是数据长度为 0 的 tcp 帧(sync 或者 pure ack)
    nxt_session_process_packet(engine, session, packetDirection, mbuf);
    return headerLen;
}

static
int tcp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "tcp");
     pschema_register_field(pschema, "srcport",  YA_FT_UINT16,  "src tcp port");
     pschema_register_field(pschema, "dstport",  YA_FT_UINT16,  "dst tcp port");

     return 0;
}

static nxt_recognizer_t *gFsTcpRecog;

static
int tcp_handoff_register(nxt_handoff_key_t key, nxt_dissector_t *dissector, void *userdata _U_)
{
    nxt_recognizer_t *recog = (nxt_recognizer_t *)gFsTcpRecog;
    return nxt_recog_register(recog, key.portPayload.serverPort, (char *)key.portPayload.payloadData, dissector);
}

static
nxt_dissector_t*  tcp_handoff_find(nxt_handoff_key_t key, nxt_session_t *session, precord_t *precord _U_, void *userdata _U_)
{
    nxt_dissector_t *recogProto = NULL;

    // 如果 session 中已经保存有识别到的协议，直接从 session 中获取;
    if (session && (recogProto = nxt_session_get_recognized_proto(NULL, session)) != NULL)
    {
        return recogProto;
    }

    // session 中还没有保存识别到的协议，进行协议识别;
    nxt_recognizer_t *recog = (nxt_recognizer_t *)gFsTcpRecog;
    recogProto = nxt_recog_do_recog(recog, key.portPayload.serverPort, key.portPayload.payloadData, key.portPayload.payloadLen);
    if (NULL == recogProto)
    {   // TODO: 此处需要引入'未知协议' 存入 session,后续不用再进行协议识别;
        return NULL;
    }

    // 识别到协议，如果 session 中还没有协议，通知 session;
    if (session && NULL == nxt_session_get_recognized_proto(NULL, session))
    {
        nxt_session_on_proto_recognized(NULL, session, recogProto);
    }

    return recogProto;
}

static
void tcp_handoff_on_resolve_done(void *userdata _U_)
{
    nxt_recognizer_t *recog = (nxt_recognizer_t *)gFsTcpRecog;
    nxt_recog_register_done(recog);
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "tcp",
    .schemaRegFun = tcp_schema_reg,
    .dissectFun   = tcp_dissect,
    .handoff      = {NXT_HANDOFF_TYPE_PORT_PAYLOAD, tcp_handoff_register, tcp_handoff_find, tcp_handoff_on_resolve_done, NULL},
    .mountAt      = {
        NXT_MNT_NUMBER("ipv4", 6),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(tcp)
{
    gFsTcpRecog = nxt_recog_create();

    nxt_dissector_register(&gDissectorDef);
}

NXT_DISSECTOR_FINI(tcp)
{
    nxt_recog_destroy(gFsTcpRecog);
}
