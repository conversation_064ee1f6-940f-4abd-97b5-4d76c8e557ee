#ifndef NXT_PARSER_H
#define NXT_PARSER_H

#include "nxt_export.h"
#include <yaBasicUtils/allocator.h>
#include <stdint.h>

typedef struct nxt_Parser nxt_parser_t;

typedef enum nxt_pstatus
{
    NXT_PSTATUS_READY   = 0, // 就绪状态，可以进行 parse;
    NXT_PSTATUS_ERROR,       // parse 出错，输入不符合协议语法;
    NXT_PSTATUS_PARTIAL,     // 输入均 parse 完毕并且符合协议语法，但输入不完整, 不够产生一个消息;
    NXT_PSTATUS_COMPLETE,    // 解析成功，识别到了一个完整的消息;
} nxt_pstatus_enum;

typedef int (*nxt_parser_init_fun)(nxt_parser_t *parser);

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

NXT_EXPORT nxt_parser_t*    nxt_parser_create_wa(ya_allocator_t *alloc, nxt_parser_init_fun initFun);
NXT_EXPORT int              nxt_parser_destroy_wa(ya_allocator_t *alloc, nxt_parser_t *parser);
NXT_EXPORT int              nxt_parser_reset(nxt_parser_t *parser);
NXT_EXPORT nxt_pstatus_enum nxt_parser_get_status(nxt_parser_t *parser);

NXT_EXPORT int nxt_parser_rtsp_init(nxt_parser_t *parser);
NXT_EXPORT int nxt_parser_rtsp_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);

// 以下接口被废弃，将使用 wa 版本;
NXT_EXPORT nxt_parser_t*    nxt_parser_create(nxt_parser_init_fun initFun) __attribute__((deprecated));
NXT_EXPORT int              nxt_parser_destroy(nxt_parser_t *parser)       __attribute__((deprecated));

#ifdef __cplusplus
}
#endif

#endif /* NXT_PARSER_H */
