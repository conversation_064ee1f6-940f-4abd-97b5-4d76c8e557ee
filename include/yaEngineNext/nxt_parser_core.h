#ifndef NXT_PARSER_INNER_H
#define NXT_PARSER_INNER_H

#include "nxt_export.h"
#include "yaEngineNext/nxt_parser.h"
#include <yaProtoRecord/precord.h>
#include <stdint.h>
#include <stdio.h>
#include <assert.h>

#define SLICE(s)                     (s[parser->top])
#define OUT_V(name, slice)           precord_put((precord_t *)userdata, name, stringn, (const char *)SLICE(slice).str, SLICE(slice).len)
#define OUT_KV(slice_k, slice_v)

// ragel action 中进行标记与提取的宏;
// TODO: 需要进行补充说明;
// p 为 ragel 中的'当前指针', 指示输入字符串中的当前位置;
#define MARK(slice, fcurs)               parser->top++;        \
                                         SLICE(slice).str = p; \
                                         nxt_parser_mark_rollback_point(parser, fcurs, p)

#define EXTRACT(slice)                   assert(p >= SLICE(slice).str); SLICE(slice).len = p - SLICE(slice).str

#ifndef NDEBUG
#define LOG_BAD_SLICE(name, slice)       printf("parse warning, bad slice %s, len is %d\n", name, SLICE(slice).len)
#else
#define LOG_BAD_SLICE(name, slice)
#endif

#define OUT(name, slice)                 EXTRACT(slice);                                                                    \
                                         if (SLICE(slice).len > 0) {OUT_V(name, slice);} else {LOG_BAD_SLICE(name, slice);} \
                                         nxt_parser_clear_rollback_point(parser);                                           \
                                         parser->top--

#define OUT_AS(name)                     OUT(name, parser->s)

#define PRINT(slice)                     EXTRACT(slice);                                                                   \
                                         if (SLICE(slice).len > 0) {printf("%.*s\n", SLICE(slice).len, SLICE(slice).str);} \
                                         nxt_parser_clear_rollback_point(parser);                                          \
                                         parser->top--

#define RAGEL_STATE_NO_ROLLBACK (-42)

typedef struct nxt_SSlice
{
    const uint8_t *str;
    uint32_t       len;
} nxt_sslice_t;

typedef struct nxt_Parser
{
    nxt_pstatus_enum     status;
    int                  ragelCS;         // ragel 中的 current state, 用于流式解析;
    const uint8_t       *ragelPoint;      // buff 中的当前位置;
    int                  ragelRollbackCS; // 记录回退点 cs 状态, 用于发现 token 被截断时的回退;
    const uint8_t       *ragelRollbackP;  // 记录回退点 p 指针;
    nxt_parser_init_fun  initFun;
    int                  top;
    nxt_sslice_t         s[10];           // 在 parser 过程执行 action 时, 默认的 slice 存储;
    nxt_sslice_t         k;               // 存储 kv 类型字段中的 k;
    nxt_sslice_t         v;               // 存储 kv 类型字段中的 v;
} nxt_parser_t;

static inline
void nxt_parser_mark_rollback_point(nxt_parser_t *parser, int fromCurrentState, const uint8_t *cur)
{
    parser->ragelRollbackCS = fromCurrentState;
    parser->ragelRollbackP  = cur;
}

static inline
void nxt_parser_clear_rollback_point(nxt_parser_t *parser)
{
    parser->ragelRollbackCS = RAGEL_STATE_NO_ROLLBACK;
    parser->ragelRollbackP  = NULL;
}

static inline
void nxt_parser_rollback(nxt_parser_t *parser)
{
    parser->ragelCS = parser->ragelRollbackCS;
    parser->ragelPoint  = parser->ragelRollbackP;
}

static inline
void nxt_parser_check_rollback(nxt_parser_t *parser, const uint8_t *cur, const uint8_t *eof)
{
    // 并且 ragel_rollback 处于一个有效状态，即位于一个待提取的 token 中间
    // 并且如果位于 eof, 说明 token 被截断，需要进行回退;
    if (parser->ragelRollbackCS != RAGEL_STATE_NO_ROLLBACK
        && cur == eof)
    {
        nxt_parser_rollback(parser);
    }
}

#endif /* NXT_PARSER_INNER_H */
