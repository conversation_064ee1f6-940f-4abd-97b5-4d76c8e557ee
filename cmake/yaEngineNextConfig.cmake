
set(engineNextTarget_cmake "${CMAKE_CURRENT_LIST_DIR}/yaEngineNextTarget.cmake")
if(EXISTS ${engineNextTarget_cmake})
  include(${engineNextTarget_cmake})
endif()

include(GNUInstallDirs) # for CMAKE_INSTALL_INCLUDEDIR

#============================================================
# ragel variables
#============================================================
set(ragel_command "ragel")
# TODO: 该目录需要调整为在 install 之后可用
set(ragel_include  ${CMAKE_SOURCE_DIR}/include/yaEngineNext)
set(ragel_include2 ${CMAKE_INSTALL_FULL_INCLUDEDIR}/yaEngineNext)
# set(ragel_depend  ${ragel_include}/nxt_parser_basic.rl)

#============================================================
# add_ragel_parser(foo.rl style)
# style: "-G2", "-F1" 等，参见 ragel --help
# 功能: 由 foo.rl ragel 文件以 style(ragel 优化级别) 生成 .c 文件;
# 导出变量: ${ragel_output} 表示生成的 .c 文件路径;
#============================================================
macro(add_ragel_parser ragel_input ragel_style)
  get_filename_component(input_basename ${ragel_input} NAME_WE)
  set(ragel_output       ${CMAKE_CURRENT_BINARY_DIR}/${input_basename}${ragel_style}.c)

  add_custom_command(OUTPUT ${ragel_output}
    # -L 不生成 #line 指令，虽然有助于报错文件跳转，但在调试时无法显示相关代码;
    # -s 报告 ragel 编译时的统计信息，例如状态机中的状态数量;
    # -I ${ragel_include} 添加 ragel 源文件中的 include 指令用于搜索的目录;
    # --state-limit 9000, 限制状态数量不超过 9000, 如果超过将拒绝编译;
    COMMAND ${ragel_command} -L -s -I ${ragel_include} -I ${ragel_include2} --state-limit=15000 ${ragel_style} -o ${ragel_output} ${ragel_input}
    DEPENDS           ${ragel_input}
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "gen ragel parser => ${ragel_output}"
  )

  # ragel 生成的 .c 文件禁用部分警告, 它可能会有警告, 但信任它;
  set_source_files_properties(${ragel_output} PROPERTIES COMPILE_OPTIONS
    "-Wno-sign-compare;-Wno-unused-function;-Wno-unused-variable;-Wno-unused-parameter")
endmacro()

#============================================================
# add_ragel_parser_list(foo.rl bar.rl)
# 功能：将 ragel 文件列表编译为一系列的 .c 文件，存储在变量 ragel_output_list 中
# 导出变量: ${ragle_output_list}
#============================================================
macro(add_ragel_parser_list)
  set(ragel_output_list "")
  foreach(input ${ARGN})
    add_ragel_parser(${input} "")
    list(APPEND ragel_output_list ${ragel_output})
  endforeach()
endmacro()

#
# macro addEngineNextPlugin
#
macro(addEngineNextPlugin plugin_name out_dir)
  set(prefix ARG)
  set(noValues TRAILER)
  set(singleValues )
  set(multiValues SOURCES RAGEL_SOURCES LINK_LIBRARIES)

  cmake_parse_arguments(${prefix}
    "${noValues}"
    "${singleValues}"
    "${multiValues}"
    ${ARGN})

  # 是否是 trailer 插件？
  if (${prefix}_TRAILER)
      set(full_plugin_name yaNxtDissector_trailer_${plugin_name})
  else()
      set(full_plugin_name yaNxtDissector_${plugin_name})
  endif()

  # 配置 sources
  set(library_sources ${${prefix}_SOURCES})

  # 是否需要编译 ragel 文件生成 .c?
  if (${prefix}_RAGEL_SOURCES)
    add_ragel_parser_list(${${prefix}_RAGEL_SOURCES})
    list(APPEND library_sources ${ragel_output_list})
  else()
  endif()

  # 创建 library
  add_library(${full_plugin_name} SHARED ${library_sources})

  # 输出目录
  if(NOT EXISTS ${out_dir})
    file(MAKE_DIRECTORY ${out_dir})
  endif()

  set_target_properties(${full_plugin_name} PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${out_dir}
    PREFIX ""           # 修改输出文件名，去掉'lib', eg: libyaNxtDissector_story.so -> yaNxtDissector_story.so
  )

  target_link_libraries(${full_plugin_name}
    PUBLIC yaEngineNext
    PUBLIC ${${prefix}_LINK_LIBRARIES}
  )
endmacro()

